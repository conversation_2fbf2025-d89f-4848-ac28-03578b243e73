# Drewry Breakbulk Sea Transport Indices Data Extractor

## Overview

This scraper extracts the Drewry Breakbulk Sea Transport Indices data from the official Drewry website. The indices track both Project Cargo and General Cargo segments to provide a comprehensive view of the breakbulk shipping market.

## Source

- **URL**: https://www.drewry.co.uk/maritime-research/maritime-research-related-content/breakbulk-sea-transport-indices
- **Data Provider**: Drewry Shipping Consultants
- **Base Point**: January 2019 = 100

## Extracted Data

The scraper extracts two main indices:

### 1. General Cargo Index
- **File**: `General_Cargo_Index_data.csv`
- **Description**: Uses TC rates of general cargo ships (3,000-20,000 dwt), small feeder containerships, handysize dry bulk carriers, car carriers, and dry container freight rates
- **Weighting**: Based on estimated proportions of cargo types and fleet composition

### 2. Project Cargo Index  
- **File**: `Project_Cargo_Index_data.csv`
- **Description**: Uses TC rates of project cargo carriers (10,000-25,000 dwt) plus all components of the General Cargo Index
- **Weighting**: Based on estimated proportions of cargo types on project cargo carriers and fleet composition

## Usage

```bash
python scripts/drewry_breakbulk_data_extractor.py
```

## Output Structure

```
data/
├── breakbulk_indices/           # Main indices (clean CSV files)
│   ├── General_Cargo_Index_data.csv
│   └── Project_Cargo_Index_data.csv
├── drewry_charts_data/          # Detailed chart data with metadata
│   └── chartblocks_00_breakbulk_indices/
│       ├── General_Cargo_Index_data.csv
│       ├── Project_Cargo_Index_data.csv
│       └── chart_metadata.json
├── drewry_raw_extracts/         # Raw extracted data
│   ├── drewry_breakbulk_charts_data.json
│   └── iframe_elements.json
└── drewry_extraction_summary.json
```

## Data Format

Each CSV file contains:
- **date**: Month-Year format (e.g., "May-2020")
- **value**: Index value (base 100 = Jan 2019)

## Technical Details

- **Chart Technology**: ChartBlocks embedded iframe
- **Data Range**: May 2020 to present (updated monthly)
- **Extraction Method**: Direct iframe data extraction from ChartBlocks API
- **Browser**: Chromium (Playwright)

## Dependencies

- playwright
- json
- csv
- pathlib
- datetime
- re

## Features

- ✅ Extracts real-time data from ChartBlocks iframe
- ✅ Handles multiple chart libraries (Highcharts, Chart.js, D3.js)
- ✅ Organized folder structure
- ✅ Comprehensive metadata and summaries
- ✅ Error handling and fallback methods
- ✅ Cross-platform filename sanitization

## Data Quality

- **Completeness**: Full historical data from May 2020
- **Frequency**: Monthly updates
- **Accuracy**: Direct extraction from official Drewry source
- **Format**: Clean CSV with proper date formatting

## Related Information

This data complements the Kline shipping indices and provides specific insight into the breakbulk and project cargo segments of the shipping market. The indices are particularly useful for:

- Breakbulk shipping market analysis
- Project cargo rate benchmarking
- General cargo market trends
- Multipurpose vessel performance tracking

## Notes

- The scraper automatically detects and handles the ChartBlocks iframe format
- Data is extracted with both timestamp and formatted date labels
- All files are saved with UTF-8 encoding for international compatibility
- The scraper includes comprehensive error handling for network timeouts and data format changes
