#!/usr/bin/env python3
"""
Rebar Shipping CLI - Specialized for steel rods (rebar) CIF pricing to Israel
Calculates: CIF = FOB (LME) + Freight + Insurance
"""

import argparse
import sys
from pathlib import Path
from datetime import datetime

# Add current directory to path for imports
sys.path.append(str(Path(__file__).parent))

from rebar_shipping_calculator import RebarShippingCalculator

def main():
    parser = argparse.ArgumentParser(
        description='Calculate rebar (steel rods) CIF prices to Israel',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
🏗️  REBAR SHIPPING EXAMPLES:

  # Get CIF quote for 5,000 tons C-500 rebar from Shanghai
  python rebar_cli.py quote --port Shanghai --tonnage 5000 --grade C-500

  # Compare all ports for 3,000 tons
  python rebar_cli.py compare --tonnage 3000

  # Show available rebar ports
  python rebar_cli.py ports

  # Get current LME FOB price
  python rebar_cli.py fob-price

  # Export CIF comparison to Excel
  python rebar_cli.py export --tonnage 8000 --format excel

📊 CIF BREAKDOWN: FOB (LME) + Freight (Baltic) + Insurance
🚢 VESSEL TYPES: Handysize/Supramax breakbulk (BSI/BHSI indices)
🔩 STEEL GRADES: C-500, B-500
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Quote command - detailed CIF breakdown
    quote_parser = subparsers.add_parser('quote', help='Get detailed CIF quote for rebar shipment')
    quote_parser.add_argument('--port', required=True, help='Origin port name')
    quote_parser.add_argument('--tonnage', type=int, required=True, help='Rebar tonnage (tons)')
    quote_parser.add_argument('--grade', choices=['C-500', 'B-500'], default='C-500', help='Steel grade')
    
    # Compare command - compare all ports
    compare_parser = subparsers.add_parser('compare', help='Compare CIF prices across all rebar ports')
    compare_parser.add_argument('--tonnage', type=int, required=True, help='Rebar tonnage (tons)')
    compare_parser.add_argument('--grade', choices=['C-500', 'B-500'], default='C-500', help='Steel grade')
    compare_parser.add_argument('--top', type=int, default=10, help='Show top N results')
    
    # Ports command - show available rebar ports
    ports_parser = subparsers.add_parser('ports', help='Show available rebar shipping ports')
    ports_parser.add_argument('--country', help='Filter by country')
    ports_parser.add_argument('--grade', help='Filter by available steel grade')
    
    # FOB price command
    fob_parser = subparsers.add_parser('fob-price', help='Get current LME FOB price')
    fob_parser.add_argument('--days', type=int, default=30, help='Days of price history')
    
    # Export command
    export_parser = subparsers.add_parser('export', help='Export CIF comparison to file')
    export_parser.add_argument('--tonnage', type=int, required=True, help='Rebar tonnage (tons)')
    export_parser.add_argument('--grade', choices=['C-500', 'B-500'], default='C-500', help='Steel grade')
    export_parser.add_argument('--format', choices=['csv', 'excel'], default='csv', help='Export format')
    export_parser.add_argument('--output', help='Output filename (auto-generated if not specified)')
    
    # Market command - show Baltic indices
    market_parser = subparsers.add_parser('market', help='Show current Baltic indices for rebar shipping')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # Initialize calculator
    try:
        print("🏗️  Initializing Rebar Shipping Calculator...")
        calculator = RebarShippingCalculator()
        print("✓ Ready")
    except Exception as e:
        print(f"❌ Error initializing calculator: {e}")
        return
    
    # Execute commands
    try:
        if args.command == 'quote':
            handle_quote_command(calculator, args)
        elif args.command == 'compare':
            handle_compare_command(calculator, args)
        elif args.command == 'ports':
            handle_ports_command(calculator, args)
        elif args.command == 'fob-price':
            handle_fob_command(calculator, args)
        elif args.command == 'export':
            handle_export_command(calculator, args)
        elif args.command == 'market':
            handle_market_command(calculator, args)
    except Exception as e:
        print(f"❌ Error executing command: {e}")

def handle_quote_command(calculator, args):
    """Handle detailed CIF quote command"""
    print(f"\n🔩 Getting CIF quote for {args.tonnage:,} tons of {args.grade} rebar from {args.port}...")
    
    try:
        calculator.get_detailed_quote(args.port, args.tonnage, args.grade)
    except ValueError as e:
        print(f"❌ Error: {e}")
        print(f"\nAvailable ports: {', '.join(calculator.rebar_ports.keys())}")
    except Exception as e:
        print(f"❌ Calculation error: {e}")

def handle_compare_command(calculator, args):
    """Handle CIF comparison command"""
    print(f"\n🏆 Comparing CIF prices for {args.tonnage:,} tons of {args.grade} rebar...")
    
    try:
        comparison = calculator.compare_rebar_routes(args.tonnage, args.grade)
        
        if comparison.empty:
            print("❌ No results available")
            return
        
        # Add freight percentage column
        comparison['Freight_Pct_of_FOB'] = (comparison['Freight_USD_per_ton'] / comparison['FOB_USD_per_ton'] * 100).round(1)

        print(f"\n📊 REBAR CIF PRICE COMPARISON ({args.grade}):")
        print("-" * 100)
        print(f"{'Rank':<4} {'Port':<12} {'Country':<8} {'FOB':<8} {'Freight':<8} {'Frt%':<6} {'Insurance':<9} {'CIF':<8} {'Total':<12}")
        print("-" * 100)

        for i, (_, row) in enumerate(comparison.head(args.top).iterrows(), 1):
            print(f"{i:<4} {row['Origin_Port']:<12} {row['Country']:<8} "
                  f"${row['FOB_USD_per_ton']:<7.0f} ${row['Freight_USD_per_ton']:<7.0f} "
                  f"{row['Freight_Pct_of_FOB']:<5.1f}% "
                  f"${row['Insurance_USD_per_ton']:<8.2f} ${row['CIF_USD_per_ton']:<7.0f} "
                  f"${row['Total_CIF_USD']:<11,.0f}")
        
        # Highlight best option
        best = comparison.iloc[0]
        print(f"\n🥇 BEST OPTION: {best['Origin_Port']} ({best['Country']})")
        print(f"   FOB: ${best['FOB_USD_per_ton']:.2f}/ton | Freight: ${best['Freight_USD_per_ton']:.2f}/ton | Insurance: ${best['Insurance_USD_per_ton']:.2f}/ton")
        print(f"   CIF: ${best['CIF_USD_per_ton']:.2f}/ton | Total: ${best['Total_CIF_USD']:,.2f}")
        
    except Exception as e:
        print(f"❌ Comparison error: {e}")

def handle_ports_command(calculator, args):
    """Handle ports listing command"""
    ports = calculator.rebar_ports
    
    # Apply filters
    if args.country:
        ports = {k: v for k, v in ports.items() if v['country'].lower() == args.country.lower()}
    
    if args.grade:
        ports = {k: v for k, v in ports.items() if args.grade in v['steel_grade_available']}
    
    if not ports:
        print("❌ No ports found matching criteria")
        return
    
    print(f"\n🚢 AVAILABLE REBAR SHIPPING PORTS ({len(ports)} total):")
    print("-" * 90)
    print(f"{'Port':<12} {'Country':<8} {'Days':<6} {'Steel Grades':<15} {'Typical Tonnage':<15}")
    print("-" * 90)
    
    for port_name, port_info in sorted(ports.items()):
        grades = ', '.join(port_info['steel_grade_available'])
        print(f"{port_name:<12} {port_info['country']:<8} {port_info['voyage_days']:<6.1f} "
              f"{grades:<15} {port_info['typical_rebar_tonnage']:<15}")

def handle_fob_command(calculator, args):
    """Handle FOB price command"""
    print(f"\n💰 Fetching current LME FOB price...")
    
    try:
        fob_data = calculator.get_lme_fob_price()
        
        print(f"\n📈 CURRENT LME FOB PRICE:")
        print("-" * 30)
        print(f"Price: ${fob_data['fob_price_usd_ton']:.2f}/ton")
        print(f"Date: {fob_data['price_date'][:10]}")
        print(f"Grade: {fob_data['grade']}")
        print(f"Source: {fob_data['source']}")
        
    except Exception as e:
        print(f"❌ Error fetching FOB price: {e}")

def handle_export_command(calculator, args):
    """Handle export command"""
    print(f"\n📊 Generating CIF comparison for export...")
    
    try:
        comparison = calculator.compare_rebar_routes(args.tonnage, args.grade)
        
        if comparison.empty:
            print("❌ No data to export")
            return
        
        # Generate filename if not provided
        if not args.output:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            extension = 'xlsx' if args.format == 'excel' else 'csv'
            args.output = f"rebar_cif_{args.tonnage}t_{args.grade}_{timestamp}.{extension}"
        
        output_path = Path("reports") / args.output
        output_path.parent.mkdir(exist_ok=True)
        
        # Export based on format
        if args.format == 'excel':
            comparison.to_excel(output_path, index=False)
        else:
            comparison.to_csv(output_path, index=False)
        
        print(f"✓ Exported {len(comparison)} routes to: {output_path}")
        
        # Show summary
        best = comparison.iloc[0]
        print(f"\nBest option: {best['Origin_Port']} at ${best['CIF_USD_per_ton']:.2f}/ton CIF")
        
    except Exception as e:
        print(f"❌ Export error: {e}")

def handle_market_command(calculator, args):
    """Handle market data command"""
    print(f"\n📈 CURRENT MARKET DATA FOR REBAR SHIPPING:")
    print("-" * 50)
    
    try:
        # Show Baltic indices
        baltic_rates = calculator._get_current_baltic_rates()
        if baltic_rates:
            print("\n🌊 Baltic Indices (Rebar-Relevant):")
            for index, data in baltic_rates.items():
                relevance = "HIGH" if index in ['BSI', 'BHSI'] else "MEDIUM"
                print(f"  {index}: {data['value']:.0f} USD/day ({relevance} relevance) - {data['date']}")
        
        # Show FOB price
        fob_data = calculator.get_lme_fob_price()
        print(f"\n💰 LME FOB Price:")
        print(f"  Steel/Rebar: ${fob_data['fob_price_usd_ton']:.2f}/ton - {fob_data['price_date'][:10]}")
        
        # Show market conditions
        print(f"\n⚙️ Market Conditions:")
        conditions = calculator.market_conditions
        print(f"  Bunker Price: ${conditions['bunker_price_usd_mt']}/MT")
        print(f"  Marine Insurance: {conditions['marine_insurance_rate']*100:.2f}% of cargo value")
        print(f"  Suez Canal Fee: ${conditions['suez_canal_fee']:,}")
        print(f"  Red Sea Surcharge: {conditions['red_sea_surcharge_rate']*100:.0f}% (China routes)")
        
    except Exception as e:
        print(f"❌ Error fetching market data: {e}")

if __name__ == '__main__':
    main()
