import requests
import json
import csv
import pandas as pd
from datetime import datetime, timedelta
from pathlib import Path

class LMEDirectAPI:
    """
    Direct API caller for LME Steel Rebar FOB Turkey prices
    Uses the discovered API endpoint to get real-time data
    """
    
    def __init__(self):
        # The API endpoint discovered by the extractor
        self.api_base_url = "https://www.lme.com/api/trading-data/chart-data"
        self.datasource_id = "d6171d98-db82-4d8b-94a4-6e3614974719"  # Steel Rebar FOB Turkey
        
        self.output_dir = Path('data')
        self.lme_data_dir = self.output_dir / 'lme_data'
        
        # Create directories
        for dir_path in [self.output_dir, self.lme_data_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
    
    def get_steel_rebar_prices(self, days_back=30):
        """
        Get steel rebar prices from LME API
        """
        print("🔗 LME Direct API - Steel Rebar FOB Turkey")
        print("=" * 50)
        
        # Calculate date range
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days_back)
        
        # Format dates for API
        start_date_str = start_date.strftime('%Y-%m-%d')
        end_date_str = end_date.strftime('%Y-%m-%d')
        
        # Build API URL
        api_url = f"{self.api_base_url}?datasourceId={self.datasource_id}&startDate={start_date_str}&endDate={end_date_str}"
        
        print(f"📡 Fetching data from: {api_url}")
        print(f"📅 Date range: {start_date_str} to {end_date_str}")
        
        try:
            # Make API request
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'en-US,en;q=0.9',
                'Referer': 'https://www.lme.com/Metals/Ferrous/LME-Steel-Rebar-FOB-Turkey-Platts'
            }
            
            response = requests.get(api_url, headers=headers, timeout=30, verify=False)
            response.raise_for_status()
            
            # Parse JSON response
            data = response.json()
            print(f"✅ API Response received (Status: {response.status_code})")
            
            # Process the data
            price_data = self._process_api_response(data)
            
            # Save the data
            self._save_price_data(price_data, data)
            
            return price_data
            
        except requests.exceptions.RequestException as e:
            print(f"❌ API Request failed: {e}")
            return None
        except json.JSONDecodeError as e:
            print(f"❌ JSON parsing failed: {e}")
            return None
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
            return None
    
    def _process_api_response(self, data):
        """
        Process the LME API response and extract price data
        """
        price_data = []
        
        try:
            # Extract labels (dates) and datasets
            labels = data.get('Labels', [])
            datasets = data.get('Datasets', [])
            
            print(f"📊 Processing {len(labels)} data points from {len(datasets)} datasets")
            
            for dataset in datasets:
                if dataset.get('Label') == 'Price':
                    prices = dataset.get('Data', [])
                    
                    # Combine dates with prices
                    for i, (date_str, price_str) in enumerate(zip(labels, prices)):
                        try:
                            # Parse date (format: MM/DD/YYYY)
                            date_obj = datetime.strptime(date_str, '%m/%d/%Y')
                            formatted_date = date_obj.strftime('%Y-%m-%d')
                            
                            # Parse price
                            price_value = float(price_str)
                            
                            price_data.append({
                                'date': formatted_date,
                                'price_usd_ton': price_value,
                                'source': 'LME_API_Direct',
                                'dataset_label': dataset.get('Label'),
                                'row_title': dataset.get('RowTitle', ''),
                                'extraction_timestamp': datetime.now().isoformat()
                            })
                            
                        except (ValueError, TypeError) as e:
                            print(f"⚠️ Error parsing data point {i}: {e}")
                            continue
            
            # Sort by date
            price_data.sort(key=lambda x: x['date'])
            
            if price_data:
                latest = price_data[-1]
                print(f"🎯 Latest Price: ${latest['price_usd_ton']:.2f}/ton ({latest['date']})")
                print(f"📈 Price Range: ${min(p['price_usd_ton'] for p in price_data):.2f} - ${max(p['price_usd_ton'] for p in price_data):.2f}")
            
        except Exception as e:
            print(f"❌ Error processing API response: {e}")
        
        return price_data
    
    def _save_price_data(self, price_data, raw_data):
        """
        Save processed price data to files
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if price_data:
            # Save detailed price data as CSV
            df = pd.DataFrame(price_data)
            csv_file = self.lme_data_dir / f'steel_rebar_prices_{timestamp}.csv'
            df.to_csv(csv_file, index=False)
            print(f"💾 Saved detailed prices: {csv_file}")
            
            # Save latest price for easy access
            latest_price = price_data[-1]
            latest_df = pd.DataFrame([{
                'date': latest_price['date'],
                'price_usd_ton': latest_price['price_usd_ton'],
                'source': 'LME_API_Direct'
            }])
            latest_file = self.lme_data_dir / 'latest_steel_price.csv'
            latest_df.to_csv(latest_file, index=False)
            print(f"💾 Saved latest price: {latest_file}")
            
            # Save price history in standard format
            history_df = df[['date', 'price_usd_ton']].copy()
            history_df.columns = ['date', 'value']
            history_df['source'] = 'LME_Steel_Rebar_FOB_Turkey'
            
            history_file = self.lme_data_dir / 'LME_Steel_Rebar_data.csv'
            history_df.to_csv(history_file, index=False)
            print(f"💾 Saved price history: {history_file}")
        
        # Save raw API response
        raw_file = self.lme_data_dir / f'lme_raw_response_{timestamp}.json'
        with open(raw_file, 'w') as f:
            json.dump(raw_data, f, indent=2, default=str)
        print(f"💾 Saved raw response: {raw_file}")
        
        # Generate summary
        summary = {
            'extraction_timestamp': datetime.now().isoformat(),
            'api_endpoint': f"{self.api_base_url}?datasourceId={self.datasource_id}",
            'data_points': len(price_data),
            'latest_price': price_data[-1] if price_data else None,
            'date_range': {
                'start': price_data[0]['date'] if price_data else None,
                'end': price_data[-1]['date'] if price_data else None
            }
        }
        
        summary_file = self.lme_data_dir / f'api_extraction_summary_{timestamp}.json'
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2, default=str)
        print(f"💾 Saved summary: {summary_file}")

def main():
    api = LMEDirectAPI()
    
    try:
        # Get last 30 days of data
        price_data = api.get_steel_rebar_prices(days_back=30)
        
        if price_data:
            print(f"\n✅ Successfully extracted {len(price_data)} price data points!")
            
            # Show recent prices
            print(f"\n📊 Recent Prices (last 5 days):")
            for price_point in price_data[-5:]:
                print(f"  {price_point['date']}: ${price_point['price_usd_ton']:.2f}/ton")
                
        else:
            print(f"\n❌ No price data extracted")
            
    except Exception as e:
        print(f"\n❌ Extraction failed: {e}")

if __name__ == "__main__":
    main()
