import pandas as pd
import numpy as np
import requests
from datetime import datetime, timedelta
from pathlib import Path
import json
from typing import Dict, Optional, Tuple

class RebarShippingCalculator:
    """
    Specialized calculator for rebar (steel rods) shipping to Israel
    Calculates CIF prices: FOB (LME) + Freight + Insurance
    Focuses on breakbulk shipping using appropriate Baltic indices
    """
    
    def __init__(self):
        self.data_path = Path(r"C:\Users\<USER>\Code\shipment\data")
        
        # Rebar-specific vessel selection (BSI/BHSI most relevant)
        self.rebar_vessel_profiles = {
            'Handysize_Breakbulk': {
                'dwt_range': (20000, 40000),
                'baltic_index': 'BHSI',  # Baltic Handysize Index
                'daily_fuel_consumption': 22,  # MT/day
                'crew_cost_daily': 1200,
                'handling_cost_per_ton': 35,  # Higher for breakbulk
                'description': 'Handysize vessels for smaller rebar shipments'
            },
            'Supramax_Breakbulk': {
                'dwt_range': (50000, 60000), 
                'baltic_index': 'BSI',   # Baltic Supramax Index
                'daily_fuel_consumption': 28,  # MT/day
                'crew_cost_daily': 1500,
                'handling_cost_per_ton': 30,
                'description': 'Supramax vessels for large rebar shipments'
            }
        }
        
        # Rebar shipping ports (focused on steel-producing regions)
        self.rebar_ports = {
            # China - Major steel producers
            "Tianjin": {"voyage_days": 52.4, "country": "China", "steel_grade_available": ["C-500", "B-500"], "typical_rebar_tonnage": "3000-8000"},
            "Shanghai": {"voyage_days": 51.5, "country": "China", "steel_grade_available": ["C-500", "B-500"], "typical_rebar_tonnage": "2000-6000"},
            "Rizhao": {"voyage_days": 51.6, "country": "China", "steel_grade_available": ["C-500"], "typical_rebar_tonnage": "5000-10000"},
            
            # Turkey - Close proximity, good steel industry
            "Iskenderun": {"voyage_days": 5.4, "country": "Turkey", "steel_grade_available": ["C-500", "B-500"], "typical_rebar_tonnage": "1000-5000"},
            "Mersin": {"voyage_days": 5.0, "country": "Turkey", "steel_grade_available": ["C-500"], "typical_rebar_tonnage": "2000-7000"},
            
            # Russia - Steel producer
            "Novorossiysk": {"voyage_days": 9.0, "country": "Russia", "steel_grade_available": ["C-500"], "typical_rebar_tonnage": "3000-8000"},
            
            # Portugal - European steel
            "Sines": {"voyage_days": 25.8, "country": "Portugal", "steel_grade_available": ["C-500", "B-500"], "typical_rebar_tonnage": "1000-4000"}
        }
        
        # Load market data
        self.baltic_data = self._load_baltic_indices()
        
        # Current market conditions for rebar shipping
        self.market_conditions = {
            'bunker_price_usd_mt': 650,
            'marine_insurance_rate': 0.0015,  # 0.15% of cargo value (typical for steel)
            'suez_canal_fee': 450000,
            'red_sea_surcharge_rate': 0.12,  # 12% for China routes
            'port_congestion_factor': 1.05   # 5% average delay factor
        }
    
    def _load_baltic_indices(self) -> pd.DataFrame:
        """Load Baltic indices focusing on BSI and BHSI for rebar shipping"""
        indices_path = self.data_path / "shipping_indices"
        
        # Focus on most relevant indices for rebar
        relevant_indices = {
            'BSI': 'BSI_data.csv',    # Primary for large rebar shipments
            'BHSI': 'BHSI_data.csv',  # Primary for smaller rebar shipments
            'BPI': 'BPI_data.csv',    # Secondary reference
            'BDI': 'BDI_data.csv'     # Market overview
        }
        
        indices = {}
        for index_name, filename in relevant_indices.items():
            file_path = indices_path / filename
            if file_path.exists():
                df = pd.read_csv(file_path, parse_dates=['date'], date_format='%Y/%m/%d')
                df['value'] = pd.to_numeric(df['value'], errors='coerce')
                df = df.dropna().drop_duplicates(subset=['date'], keep='last').set_index('date')
                if not df.empty:
                    indices[index_name] = df
        
        if indices:
            combined = pd.concat(indices.values(), axis=1, keys=indices.keys())
            return combined.ffill()
        return pd.DataFrame()
    
    def get_lme_fob_price(self, start_date: str = None, end_date: str = None) -> Dict:
        """
        Get LME steel/rebar FOB price from scraped data files first, then try API
        """
        # First, try to load from scraped data
        lme_data_path = self.data_path / "lme_data"

        # Check for latest scraped price
        latest_file = lme_data_path / "latest_steel_price.csv"
        if latest_file.exists():
            try:
                print(f"[INFO] Loading LME price from scraped data: {latest_file}")
                df = pd.read_csv(latest_file)
                if not df.empty:
                    latest_row = df.iloc[-1]
                    return {
                        'fob_price_usd_ton': float(latest_row['price_usd_ton']),
                        'price_date': latest_row['date'],
                        'currency': 'USD',
                        'unit': 'per_metric_ton',
                        'grade': 'Steel/Rebar (scraped)',
                        'source': f"LME Scraper ({latest_row['source']})"
                    }
            except Exception as e:
                print(f"[WARNING] Could not load scraped LME data: {e}")

        # Check for any recent steel price files
        if lme_data_path.exists():
            steel_files = list(lme_data_path.glob("steel_prices_*.csv"))
            if steel_files:
                # Get the most recent file
                latest_steel_file = max(steel_files, key=lambda x: x.stat().st_mtime)
                try:
                    print(f"[INFO] Loading from recent steel price file: {latest_steel_file.name}")
                    df = pd.read_csv(latest_steel_file)
                    if not df.empty:
                        # Get the most recent price
                        latest_row = df.iloc[-1]
                        return {
                            'fob_price_usd_ton': float(latest_row['price_usd_ton']),
                            'price_date': latest_row['date'],
                            'currency': 'USD',
                            'unit': 'per_metric_ton',
                            'grade': 'Steel/Rebar (scraped)',
                            'source': f"LME Scraper ({latest_row.get('source', 'scraped')})"
                        }
                except Exception as e:
                    print(f"[WARNING] Could not load steel price file: {e}")

        # If no scraped data available, try API (but expect it to fail)
        if not start_date:
            start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        if not end_date:
            end_date = datetime.now().strftime('%Y-%m-%d')

        lme_api_url = f"https://www.lme.com/api/trading-data/chart-data?datasourceId=d6171d98-db82-4d8b-94a4-6e3614974719&startDate={start_date}&endDate={end_date}"

        try:
            print(f"[INFO] Trying LME API: {lme_api_url}")
            response = requests.get(lme_api_url, timeout=10, verify=False)  # Skip SSL verification
            response.raise_for_status()

            lme_data = response.json()

            # Extract latest price
            if lme_data and 'data' in lme_data:
                latest_price = lme_data['data'][-1] if lme_data['data'] else None
                if latest_price:
                    return {
                        'fob_price_usd_ton': float(latest_price.get('value', 539.5)),
                        'price_date': latest_price.get('date', datetime.now().isoformat()),
                        'currency': 'USD',
                        'unit': 'per_metric_ton',
                        'grade': 'Steel/Rebar (API)',
                        'source': 'LME API'
                    }

        except Exception as e:
            print(f"[WARNING] LME API failed: {e}")

        # Final fallback
        print("[INFO] Using fallback FOB price - consider running LME scraper")
        return {
            'fob_price_usd_ton': 539.5,  # Your example price
            'price_date': datetime.now().strftime('%Y-%m-%d'),
            'currency': 'USD',
            'unit': 'per_metric_ton',
            'grade': 'C-500 (fallback)',
            'source': 'Fallback - run scripts/lme_data_scraper.py for current prices'
        }
    
    def calculate_rebar_cif_price(self, origin_port: str, rebar_tonnage: int, 
                                  steel_grade: str = "C-500") -> Dict:
        """
        Calculate complete CIF price for rebar shipment
        CIF = FOB + Freight + Insurance
        """
        
        if origin_port not in self.rebar_ports:
            raise ValueError(f"Port {origin_port} not available for rebar shipping")
        
        port_info = self.rebar_ports[origin_port]
        
        # Check if port handles the requested steel grade
        if steel_grade not in port_info['steel_grade_available']:
            print(f"[WARNING] {steel_grade} may not be available at {origin_port}")
        
        # 1. GET FOB PRICE from LME
        fob_data = self.get_lme_fob_price()
        fob_price_per_ton = fob_data['fob_price_usd_ton']
        
        # 2. CALCULATE FREIGHT COST
        freight_cost_per_ton = self._calculate_rebar_freight(origin_port, rebar_tonnage)
        
        # 3. CALCULATE INSURANCE COST
        cargo_value_total = fob_price_per_ton * rebar_tonnage
        insurance_cost_total = cargo_value_total * self.market_conditions['marine_insurance_rate']
        insurance_cost_per_ton = insurance_cost_total / rebar_tonnage
        
        # 4. CALCULATE CIF COMPONENTS
        cif_price_per_ton = fob_price_per_ton + freight_cost_per_ton + insurance_cost_per_ton
        total_cif_value = cif_price_per_ton * rebar_tonnage
        
        return {
            'shipment_details': {
                'origin_port': origin_port,
                'destination': 'Israel (Ashdod/Haifa)',
                'steel_grade': steel_grade,
                'tonnage': rebar_tonnage,
                'voyage_days': port_info['voyage_days'],
                'country': port_info['country']
            },
            'cif_breakdown': {
                'fob_price_per_ton': round(fob_price_per_ton, 2),
                'freight_cost_per_ton': round(freight_cost_per_ton, 2),
                'insurance_cost_per_ton': round(insurance_cost_per_ton, 2),
                'cif_price_per_ton': round(cif_price_per_ton, 2)
            },
            'total_costs': {
                'fob_total': round(fob_price_per_ton * rebar_tonnage, 2),
                'freight_total': round(freight_cost_per_ton * rebar_tonnage, 2),
                'insurance_total': round(insurance_cost_total, 2),
                'cif_total': round(total_cif_value, 2)
            },
            'market_data': {
                'fob_source': fob_data,
                'baltic_rates_used': self._get_current_baltic_rates(),
                'calculation_date': datetime.now().isoformat()
            }
        }
    
    def _calculate_rebar_freight(self, origin_port: str, rebar_tonnage: int) -> float:
        """Calculate freight cost per ton for rebar breakbulk shipping"""

        port_info = self.rebar_ports[origin_port]
        voyage_days = port_info['voyage_days']

        # Select appropriate vessel type based on tonnage
        if rebar_tonnage <= 3000:
            vessel_profile = self.rebar_vessel_profiles['Handysize_Breakbulk']
            typical_vessel_capacity = 25000  # Handysize capacity
        else:
            vessel_profile = self.rebar_vessel_profiles['Supramax_Breakbulk']
            typical_vessel_capacity = 55000  # Supramax capacity

        # Get Baltic index rate (these are TCE rates in USD/day)
        baltic_index = vessel_profile['baltic_index']
        if baltic_index in self.baltic_data.columns and not self.baltic_data.empty:
            daily_tce_rate = self.baltic_data[baltic_index].iloc[-1]['value']
        else:
            # Fallback TCE rates (Time Charter Equivalent)
            daily_tce_rate = 10500 if baltic_index == 'BSI' else 8500

        # CORRECTED CALCULATION:
        # Baltic indices are TCE (Time Charter Equivalent) rates that already include:
        # - Vessel operating costs
        # - Fuel costs
        # - Port costs
        # We should use them more directly, not add all costs again

        # Base freight calculation using TCE rate
        # TCE rate is for the whole vessel per day, so we need to allocate based on cargo
        vessel_utilization = min(rebar_tonnage / typical_vessel_capacity, 1.0)  # Max 100%

        # Calculate freight components more realistically
        base_charter_cost = daily_tce_rate * voyage_days * vessel_utilization

        # Additional costs specific to rebar breakbulk
        handling_cost_total = vessel_profile['handling_cost_per_ton'] * rebar_tonnage

        # Canal fees (only if route requires Suez Canal)
        canal_cost = 0
        if port_info['country'] in ['China', 'Russia', 'Portugal']:
            # Allocate canal cost based on cargo proportion
            canal_cost = self.market_conditions['suez_canal_fee'] * vessel_utilization

        # Red Sea surcharge for China routes (applied to base freight only)
        red_sea_surcharge = 0
        if port_info['country'] == 'China':
            red_sea_surcharge = base_charter_cost * self.market_conditions['red_sea_surcharge_rate']

        # Total freight cost
        total_freight = base_charter_cost + handling_cost_total + canal_cost + red_sea_surcharge

        # Apply minor congestion factor
        total_freight *= self.market_conditions['port_congestion_factor']

        # Return cost per ton
        freight_per_ton = total_freight / rebar_tonnage

        # Apply minimum freight charge for very small shipments
        # Small shipments still need minimum vessel space and handling
        if rebar_tonnage < 100:  # Less than 100 tons
            min_freight_by_port = {
                'Mersin': 25, 'Iskenderun': 25,  # Turkey - close proximity
                'Novorossiysk': 35,  # Russia - medium distance
                'Sines': 40,  # Portugal - longer distance
                'Shanghai': 50, 'Rizhao': 50, 'Tianjin': 50  # China - longest distance
            }
            min_freight = min_freight_by_port.get(origin_port, 30)
            freight_per_ton = max(freight_per_ton, min_freight)

        # Sanity check: freight should typically be $20-150/ton for most routes
        if freight_per_ton > 200:
            print(f"[WARNING] High freight cost calculated: ${freight_per_ton:.2f}/ton for {origin_port}")
            # Apply a reasonable cap for very long routes
            freight_per_ton = min(freight_per_ton, 150)

        return freight_per_ton
    
    def _get_current_baltic_rates(self) -> Dict:
        """Get current Baltic rates for reference"""
        rates = {}
        if not self.baltic_data.empty:
            for col in self.baltic_data.columns:
                index_name = col[0] if isinstance(col, tuple) else col
                if index_name in ['BSI', 'BHSI', 'BPI', 'BDI']:
                    rates[index_name] = {
                        'value': float(self.baltic_data[col].iloc[-1]),
                        'date': self.baltic_data.index[-1].strftime('%Y-%m-%d')
                    }
        return rates
    
    def compare_rebar_routes(self, rebar_tonnage: int, steel_grade: str = "C-500") -> pd.DataFrame:
        """Compare rebar CIF prices across all available ports"""
        
        results = []
        for port in self.rebar_ports.keys():
            try:
                cif_calc = self.calculate_rebar_cif_price(port, rebar_tonnage, steel_grade)
                
                results.append({
                    'Origin_Port': port,
                    'Country': cif_calc['shipment_details']['country'],
                    'Voyage_Days': cif_calc['shipment_details']['voyage_days'],
                    'FOB_USD_per_ton': cif_calc['cif_breakdown']['fob_price_per_ton'],
                    'Freight_USD_per_ton': cif_calc['cif_breakdown']['freight_cost_per_ton'],
                    'Insurance_USD_per_ton': cif_calc['cif_breakdown']['insurance_cost_per_ton'],
                    'CIF_USD_per_ton': cif_calc['cif_breakdown']['cif_price_per_ton'],
                    'Total_CIF_USD': cif_calc['total_costs']['cif_total'],
                    'Steel_Grade': steel_grade,
                    'Tonnage': rebar_tonnage
                })
            except Exception as e:
                print(f"[ERROR] Failed to calculate for {port}: {e}")
                continue
        
        df = pd.DataFrame(results)
        if not df.empty:
            df = df.sort_values('CIF_USD_per_ton')
        return df

    def get_detailed_quote(self, origin_port: str, rebar_tonnage: int,
                          steel_grade: str = "C-500") -> None:
        """Print detailed CIF breakdown for a specific route"""

        result = self.calculate_rebar_cif_price(origin_port, rebar_tonnage, steel_grade)

        print(f"\n🏗️  DETAILED REBAR QUOTE")
        print("=" * 50)
        print(f"📍 Route: {origin_port} ({result['shipment_details']['country']}) → Israel")
        print(f"🔩 Steel Grade: {steel_grade}")
        print(f"⚖️  Tonnage: {rebar_tonnage:,} tons")
        print(f"🚢 Voyage Time: {result['shipment_details']['voyage_days']} days")

        print(f"\n💰 CIF PRICE BREAKDOWN:")
        print("-" * 30)
        print(f"FOB Price (LME):     ${result['cif_breakdown']['fob_price_per_ton']:>8.2f}/ton")
        print(f"Freight Cost:        ${result['cif_breakdown']['freight_cost_per_ton']:>8.2f}/ton")
        print(f"Insurance Cost:      ${result['cif_breakdown']['insurance_cost_per_ton']:>8.2f}/ton")
        print("-" * 30)
        print(f"CIF Price Total:     ${result['cif_breakdown']['cif_price_per_ton']:>8.2f}/ton")

        print(f"\n📊 TOTAL SHIPMENT COSTS:")
        print("-" * 30)
        print(f"FOB Total:           ${result['total_costs']['fob_total']:>12,.2f}")
        print(f"Freight Total:       ${result['total_costs']['freight_total']:>12,.2f}")
        print(f"Insurance Total:     ${result['total_costs']['insurance_total']:>12,.2f}")
        print("-" * 30)
        print(f"CIF Total:           ${result['total_costs']['cif_total']:>12,.2f}")

        print(f"\n📈 Market Data Used:")
        fob_source = result['market_data']['fob_source']
        print(f"FOB Source: {fob_source['source']}")
        print(f"FOB Date: {fob_source['price_date'][:10]}")

        baltic_rates = result['market_data']['baltic_rates_used']
        if baltic_rates:
            print("Baltic Indices:")
            for index, data in baltic_rates.items():
                print(f"  {index}: {data['value']:.0f} (as of {data['date']})")

if __name__ == "__main__":
    print("🏗️  REBAR SHIPPING CALCULATOR FOR ISRAEL")
    print("=" * 50)

    calculator = RebarShippingCalculator()

    # Example calculation
    tonnage = 5000  # 5,000 tons of rebar
    steel_grade = "C-500"

    print(f"\nCalculating CIF prices for {tonnage:,} tons of {steel_grade} rebar...")

    # Show detailed quote for best port first
    try:
        calculator.get_detailed_quote("Mersin", tonnage, steel_grade)
    except Exception as e:
        print(f"Error getting detailed quote: {e}")

    # Compare all routes
    comparison = calculator.compare_rebar_routes(tonnage, steel_grade)

    if not comparison.empty:
        print(f"\n🏆 REBAR CIF PRICE COMPARISON ({steel_grade}):")
        print("-" * 80)
        print(f"{'Port':<12} {'Country':<8} {'FOB':<8} {'Freight':<8} {'Insurance':<9} {'CIF':<8} {'Total':<12}")
        print("-" * 80)

        for _, row in comparison.head(10).iterrows():
            print(f"{row['Origin_Port']:<12} {row['Country']:<8} "
                  f"${row['FOB_USD_per_ton']:<7.0f} ${row['Freight_USD_per_ton']:<7.0f} "
                  f"${row['Insurance_USD_per_ton']:<8.2f} ${row['CIF_USD_per_ton']:<7.0f} "
                  f"${row['Total_CIF_USD']:<11,.0f}")

        # Show best option details
        best = comparison.iloc[0]
        print(f"\n🥇 BEST OPTION: {best['Origin_Port']} ({best['Country']})")
        print(f"   CIF Price: ${best['CIF_USD_per_ton']:.2f}/ton")
        print(f"   Total Cost: ${best['Total_CIF_USD']:,.2f}")
        print(f"   Voyage Time: {best['Voyage_Days']} days")

        # Save results
        output_dir = Path("reports")
        output_dir.mkdir(exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        comparison.to_csv(output_dir / f"rebar_cif_prices_{timestamp}.csv", index=False)
        print(f"\n📊 Results saved to: reports/rebar_cif_prices_{timestamp}.csv")
    else:
        print("❌ No results generated. Check data availability.")
