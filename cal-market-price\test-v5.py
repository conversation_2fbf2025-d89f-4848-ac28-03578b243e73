import pandas as pd
import numpy as np
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime

class ShippingDataIntegrator:
    def __init__(self):
        self.data_path = Path(r"C:\Users\<USER>\Code\shipment\data")
        self.breakbulk_path = self.data_path / "breakbulk_indices"
        
    def load_drewry_indices(self):
        """Load Drewry's General and Project Cargo Indices"""
        print("[INFO] Loading Drewry indices from:", self.breakbulk_path)
        indices = {}
        for file in self.breakbulk_path.glob('*_Index_data.csv'):
            print(f"[INFO] Reading Drewry file: {file}")
            name = file.stem.replace('_data', '')
            df = pd.read_csv(
                file,
                parse_dates=['date'],
                date_format='%b-%Y'  # e.g., Apr-2025
            )
            print(f"[DEBUG] {name} - Loaded {len(df)} rows before cleaning.")
            df['value'] = pd.to_numeric(df['value'], errors='coerce')
            df = df.dropna().set_index('date')
            print(f"[DEBUG] {name} - {len(df)} rows after cleaning.")
            if df.empty:
                print(f"[ERROR] Drewry index DataFrame for {name} is empty after cleaning. Check file: {file}")
                raise ValueError(f"Drewry index DataFrame for {name} is empty after cleaning. Check file: {file}")
            indices[name] = df
        if not indices:
            print("[ERROR] No Drewry index files found or all are empty.")
            raise ValueError("No Drewry index files found or all are empty.")
        print(f"[INFO] Drewry indices loaded: {list(indices.keys())}")
        # Merge indices with forward fill for missing dates
        combined = pd.concat(indices.values(), axis=1, keys=indices.keys())
        print(f"[INFO] Combined Drewry DataFrame shape: {combined.shape}")
        return combined.ffill()

    def load_baltic_indices(self):
        """Load Baltic indices (e.g., BSI) from shipping_indices folder"""
        bsi_path = self.data_path / "shipping_indices" / "BSI_data.csv"
        print(f"[INFO] Loading Baltic index from: {bsi_path}")
        if bsi_path.exists():
            df = pd.read_csv(
                bsi_path,
                parse_dates=['date'],
                date_format='%Y/%m/%d'  # e.g., 2025/6/20
            )
            print(f"[DEBUG] BSI - Loaded {len(df)} rows before cleaning.")
            df['value'] = pd.to_numeric(df['value'], errors='coerce')
            df = df.dropna().set_index('date')
            print(f"[DEBUG] BSI - {len(df)} rows after cleaning.")
            if df.empty:
                print(f"[ERROR] BSI DataFrame is empty after cleaning. Check file: {bsi_path}")
                raise ValueError(f"BSI DataFrame is empty after cleaning. Check file: {bsi_path}")
            print(f"[INFO] BSI DataFrame shape: {df.shape}")
            return df
        else:
            print(f"[ERROR] BSI_data.csv not found at {bsi_path}")
            raise FileNotFoundError(f"BSI_data.csv not found at {bsi_path}")

class BreakbulkCostEngine:
    cargo_type_to_index = {
        'General_Cargo': 'General_Cargo_Index',
        'Project_Cargo': 'Project_Cargo_Index'
    }
    # Add voyage days mapping
    voyage_days_map = {
        "Tianjin": 52.4, "Rizhao": 51.6, "Shanghai": 51.5,
        "Novorossiysk": 9.0,
        "Iskenderun": 5.4, "Mersin": 5.0, "Nemrut Bay": 7.0,
        "Sines": 25.8, "Leixões": 26.5
    }
    # Add port to country mapping
    port_to_country = {
        "Tianjin": "China", "Rizhao": "China", "Shanghai": "China",
        "Novorossiysk": "Russia",
        "Iskenderun": "Turkey", "Mersin": "Turkey", "Nemrut Bay": "Turkey",
        "Sines": "Portugal", "Leixões": "Portugal"
    }
    def __init__(self, drewry_data, bsi_data):
        self.drewry = drewry_data
        self.bsi = bsi_data
        # Enhanced vessel parameters
        self.vessel_profiles = {
            'General_Cargo': {
                'dwt_range': (3000, 20000),
                'base_rate': 8500,
                'index_weight': 0.7,  # Higher weight to Drewry
                'handling_cost': 25
            },
            'Project_Cargo': {
                'dwt_range': (10000, 25000),
                'base_rate': 12000,
                'index_weight': 0.9,  # Even higher Drewry weight
                'handling_cost': 40
            }
        }
        # Region-specific adjustments
        self.region_factors = {
            'China': {'General': 1.15, 'Project': 1.25},
            'Mediterranean': {'General': 0.95, 'Project': 1.05},
            'default': {'General': 1.0, 'Project': 1.1}
        }

    def calculate_route_cost(self, origin_port, cargo_type='General_Cargo', dwt_used=5000):
        """Estimate cost per tonne for a shipment from given origin port to Israel."""
        port = origin_port.split('-')[0] if '-' in origin_port else origin_port
        voyage_days = self.voyage_days_map.get(port)
        if voyage_days is None:
            raise ValueError(f"Voyage time for {origin_port} not found.")

        drewry_key = self.cargo_type_to_index.get(cargo_type, cargo_type)
        drewry_idx = self.drewry[drewry_key].iloc[-1]['value']
        bsi_idx = self.bsi.iloc[-1, 0]  # Assuming daily $/day

        profile = self.vessel_profiles[cargo_type]

        # Blended rate calculation ($/day)
        blended_daily_rate = (bsi_idx * (1 - profile['index_weight']) + drewry_idx * profile['index_weight'])

        # Voyage cost in USD for whole ship
        voyage_cost_total = blended_daily_rate * voyage_days + profile['handling_cost']

        # Cost per metric ton (estimate using effective dwt)
        cost_per_ton = voyage_cost_total / dwt_used

        # Apply region factor
        region = self._map_region(origin_port)
        region_adj = self.region_factors.get(region, self.region_factors['default'])[cargo_type.split('_')[0]]
        return round(cost_per_ton * region_adj, 2)

    def _map_region(self, port):
        if port in ['Tianjin', 'Rizhao', 'Shanghai']:
            return 'China'
        elif port in ['Iskenderun', 'Mersin', 'Nemrut Bay']:
            return 'Mediterranean'
        elif port in ['Novorossiysk']:
            return 'Russia'
        elif port in ['Sines', 'Leixões']:
            return 'Portugal'
        else:
            return 'default'

# Implementation
if __name__ == "__main__":
    print("[INFO] Starting shipping rate calculation script...")
    integrator = ShippingDataIntegrator()
    # Load all required indices
    drewry_data = integrator.load_drewry_indices()
    bsi_data = integrator.load_baltic_indices()
    print(f"[INFO] Drewry DataFrame columns: {drewry_data.columns}")
    print(f"[INFO] BSI DataFrame columns: {bsi_data.columns}")
    # Initialize calculator
    calculator = BreakbulkCostEngine(drewry_data, bsi_data)
    # Calculate rates for all combinations
    origin_ports = ["Tianjin", "Rizhao", "Shanghai", "Novorossiysk", "Iskenderun", "Mersin", "Nemrut Bay", "Sines", "Leixões"]
    cargo_types = ['General_Cargo', 'Project_Cargo']
    results = []
    for port in origin_ports:
        for cargo_type in cargo_types:
            print(f"[INFO] Calculating rate for {port} - {cargo_type}")
            rate = calculator.calculate_route_cost(port, cargo_type)
            country = calculator.port_to_country.get(port, "Unknown")
            print(f"[RESULT] {port} ({country}) - {cargo_type}: {rate} USD/ton")
            results.append({
                'Origin_Country': country,
                'Origin_Port': port,
                'Cargo_Type': cargo_type,
                'Rate_USD_per_ton': rate,
                'Calculation_Date': datetime.now().date()
            })
    # Generate report and visualizations
    report = pd.DataFrame(results)
    # Visualization
    report['Port_Label'] = report['Origin_Port'] + ' (' + report['Origin_Country'] + ')'
    plt.figure(figsize=(14, 7))
    sns.barplot(data=report, x='Port_Label', y='Rate_USD_per_ton', hue='Cargo_Type')
    plt.title('Breakbulk Shipping Rates Comparison (General vs Project Cargo)')
    plt.ylabel('USD per Metric Ton')
    plt.xticks(rotation=45)
    plt.tight_layout()
    # Save outputs
    output_dir = Path(r"C:\Users\<USER>\Code\shipment\reports")
    output_dir.mkdir(exist_ok=True)
    report.to_csv(output_dir / "drewry_breakbulk_cost_report.csv", index=False)
    plt.savefig(output_dir / "drewry_rates_comparison.png")