import pandas as pd
import numpy as np
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime

class ShippingDataIntegrator:
    def __init__(self):
        self.data_path = Path(r"C:\Users\<USER>\Code\shipment\data")
        self.breakbulk_path = self.data_path / "breakbulk_indices"
        
    def load_drewry_indices(self):
        """Load Drewry's General and Project Cargo Indices"""
        print("[INFO] Loading Drewry indices from:", self.breakbulk_path)
        indices = {}
        for file in self.breakbulk_path.glob('*_Index_data.csv'):
            print(f"[INFO] Reading Drewry file: {file}")
            name = file.stem.replace('_data', '')
            df = pd.read_csv(
                file,
                parse_dates=['date'],
                date_format='%b-%Y'  # e.g., Apr-2025
            )
            print(f"[DEBUG] {name} - Loaded {len(df)} rows before cleaning.")
            df['value'] = pd.to_numeric(df['value'], errors='coerce')
            df = df.dropna().set_index('date')
            print(f"[DEBUG] {name} - {len(df)} rows after cleaning.")
            if df.empty:
                print(f"[ERROR] Drewry index DataFrame for {name} is empty after cleaning. Check file: {file}")
                raise ValueError(f"Drewry index DataFrame for {name} is empty after cleaning. Check file: {file}")
            indices[name] = df
        if not indices:
            print("[ERROR] No Drewry index files found or all are empty.")
            raise ValueError("No Drewry index files found or all are empty.")
        print(f"[INFO] Drewry indices loaded: {list(indices.keys())}")
        # Merge indices with forward fill for missing dates
        combined = pd.concat(indices.values(), axis=1, keys=indices.keys())
        print(f"[INFO] Combined Drewry DataFrame shape: {combined.shape}")
        return combined.ffill()

    def load_baltic_indices(self):
        """Load all available Baltic indices from shipping_indices folder"""
        indices_path = self.data_path / "shipping_indices"
        print(f"[INFO] Loading Baltic indices from: {indices_path}")

        # Baltic indices to load
        baltic_files = {
            'BSI': 'BSI_data.csv',      # Baltic Supramax Index
            'BDI': 'BDI_data.csv',      # Baltic Dry Index
            'BCI': 'BCI_data.csv',      # Baltic Capesize Index
            'BPI': 'BPI_data.csv',      # Baltic Panamax Index
            'BHSI': 'BHSI_data.csv',    # Baltic Handysize Index
            'VLCC': 'VLCC_data.csv'     # VLCC Tanker rates
        }

        indices = {}
        for index_name, filename in baltic_files.items():
            file_path = indices_path / filename
            if file_path.exists():
                print(f"[INFO] Loading {index_name} from: {file_path}")
                df = pd.read_csv(
                    file_path,
                    parse_dates=['date'],
                    date_format='%Y/%m/%d'  # e.g., 2025/6/20
                )
                print(f"[DEBUG] {index_name} - Loaded {len(df)} rows before cleaning.")
                df['value'] = pd.to_numeric(df['value'], errors='coerce')
                df = df.dropna()

                # Handle duplicate dates by taking the last value
                df = df.drop_duplicates(subset=['date'], keep='last')
                df = df.set_index('date')

                print(f"[DEBUG] {index_name} - {len(df)} rows after cleaning and deduplication.")
                if not df.empty:
                    indices[index_name] = df
                    print(f"[INFO] {index_name} DataFrame shape: {df.shape}")
                else:
                    print(f"[WARNING] {index_name} DataFrame is empty after cleaning.")
            else:
                print(f"[WARNING] {filename} not found, skipping {index_name}")

        if not indices:
            raise ValueError("No Baltic indices found or all are empty.")

        # Combine all indices
        combined = pd.concat(indices.values(), axis=1, keys=indices.keys())
        print(f"[INFO] Combined Baltic indices shape: {combined.shape}")
        print(f"[INFO] Available Baltic indices: {list(indices.keys())}")
        return combined.ffill()  # Forward fill missing values

class BreakbulkCostEngine:
    cargo_type_to_index = {
        'General_Cargo': 'General_Cargo_Index',
        'Project_Cargo': 'Project_Cargo_Index'
    }

    # Expanded voyage days mapping (from manual/Voyage_Times_to_Ashdod_Haifa.md)
    voyage_days_map = {
        # China ports
        "Tianjin": 52.4, "Rizhao": 51.6, "Shanghai": 51.5,
        # Russia ports
        "Novorossiysk": 9.0,
        # Turkey ports
        "Iskenderun": 5.4, "Mersin": 5.0, "Nemrut Bay": 7.0,
        # Portugal ports
        "Sines": 25.8, "Leixões": 26.5,
        # Additional potential ports (estimated based on regional patterns)
        "Qingdao": 51.8, "Dalian": 52.0, "Ningbo": 51.2,  # China
        "Izmir": 6.5, "Aliaga": 6.8,  # Turkey
        "Constanta": 8.5, "Varna": 8.2,  # Black Sea
        "Valencia": 24.5, "Barcelona": 25.0, "Bilbao": 26.0,  # Spain
        "Genoa": 22.0, "La Spezia": 22.5,  # Italy
        "Marseille": 23.0, "Le Havre": 27.0,  # France
        "Hamburg": 28.5, "Bremen": 28.8,  # Germany
        "Rotterdam": 27.5, "Amsterdam": 27.8,  # Netherlands
        "Antwerp": 27.2,  # Belgium
        "Gdansk": 26.8, "Gdynia": 27.0,  # Poland
    }

    # Expanded port to country mapping
    port_to_country = {
        # China
        "Tianjin": "China", "Rizhao": "China", "Shanghai": "China",
        "Qingdao": "China", "Dalian": "China", "Ningbo": "China",
        # Russia
        "Novorossiysk": "Russia",
        # Turkey
        "Iskenderun": "Turkey", "Mersin": "Turkey", "Nemrut Bay": "Turkey",
        "Izmir": "Turkey", "Aliaga": "Turkey",
        # Portugal
        "Sines": "Portugal", "Leixões": "Portugal",
        # Spain
        "Valencia": "Spain", "Barcelona": "Spain", "Bilbao": "Spain",
        # Italy
        "Genoa": "Italy", "La Spezia": "Italy",
        # France
        "Marseille": "France", "Le Havre": "France",
        # Germany
        "Hamburg": "Germany", "Bremen": "Germany",
        # Netherlands
        "Rotterdam": "Netherlands", "Amsterdam": "Netherlands",
        # Belgium
        "Antwerp": "Belgium",
        # Poland
        "Gdansk": "Poland", "Gdynia": "Poland",
        # Bulgaria/Romania
        "Constanta": "Romania", "Varna": "Bulgaria"
    }
    def __init__(self, drewry_data, baltic_data):
        self.drewry = drewry_data
        self.baltic = baltic_data
        # Enhanced vessel parameters
        self.vessel_profiles = {
            'General_Cargo': {
                'dwt_range': (3000, 20000),
                'base_rate': 8500,
                'index_weight': 0.7,  # Higher weight to Drewry
                'handling_cost': 25
            },
            'Project_Cargo': {
                'dwt_range': (10000, 25000),
                'base_rate': 12000,
                'index_weight': 0.9,  # Even higher Drewry weight
                'handling_cost': 40
            }
        }
        # Enhanced region-specific adjustments
        self.region_factors = {
            'China': {'General': 1.15, 'Project': 1.25},  # Long distance, Red Sea risk
            'Russia': {'General': 1.05, 'Project': 1.15},  # Black Sea, geopolitical risk
            'Turkey': {'General': 0.95, 'Project': 1.05},  # Close proximity, Mediterranean
            'Mediterranean': {'General': 0.95, 'Project': 1.05},  # Spain, Italy, France
            'Northern_Europe': {'General': 1.08, 'Project': 1.18},  # Germany, Netherlands, Belgium
            'Eastern_Europe': {'General': 1.02, 'Project': 1.12},  # Poland, Romania, Bulgaria
            'Portugal': {'General': 1.0, 'Project': 1.1},  # Atlantic route
            'default': {'General': 1.0, 'Project': 1.1}
        }

    def calculate_route_cost(self, origin_port, cargo_type='General_Cargo', dwt_used=5000):
        """Estimate cost per tonne for a shipment from given origin port to Israel."""
        port = origin_port.split('-')[0] if '-' in origin_port else origin_port
        voyage_days = self.voyage_days_map.get(port)
        if voyage_days is None:
            raise ValueError(f"Voyage time for {origin_port} not found.")

        # Get Drewry index
        drewry_key = self.cargo_type_to_index.get(cargo_type, cargo_type)
        drewry_idx = self.drewry[drewry_key].iloc[-1]['value']

        # Get appropriate Baltic index based on cargo type and vessel size
        baltic_rate = self._get_optimal_baltic_rate(cargo_type, dwt_used)

        profile = self.vessel_profiles[cargo_type]

        # Enhanced blended rate calculation ($/day)
        # Use Drewry as primary for breakbulk, Baltic as market adjustment
        blended_daily_rate = (baltic_rate * (1 - profile['index_weight']) +
                             drewry_idx * profile['index_weight'])

        # Voyage cost in USD for whole ship
        voyage_cost_total = blended_daily_rate * voyage_days + profile['handling_cost']

        # Cost per metric ton (estimate using effective dwt)
        cost_per_ton = voyage_cost_total / dwt_used

        # Apply region factor
        region = self._map_region(origin_port)
        region_adj = self.region_factors.get(region, self.region_factors['default'])[cargo_type.split('_')[0]]

        return round(cost_per_ton * region_adj, 2)

    def _get_optimal_baltic_rate(self, cargo_type, dwt_used):
        """Select the most appropriate Baltic index based on cargo type and vessel size"""
        # Priority order for different vessel sizes and cargo types
        if cargo_type == 'General_Cargo':
            if dwt_used <= 10000:
                # Small vessels - use BHSI (Handysize) if available
                if 'BHSI' in self.baltic.columns:
                    return self.baltic['BHSI'].iloc[-1]['value']
            elif dwt_used <= 20000:
                # Medium vessels - use BSI (Supramax) if available
                if 'BSI' in self.baltic.columns:
                    return self.baltic['BSI'].iloc[-1]['value']

        elif cargo_type == 'Project_Cargo':
            # Project cargo typically uses larger, specialized vessels
            if dwt_used <= 15000:
                if 'BSI' in self.baltic.columns:
                    return self.baltic['BSI'].iloc[-1]['value']
            elif dwt_used <= 25000:
                if 'BPI' in self.baltic.columns:
                    return self.baltic['BPI'].iloc[-1]['value']

        # Fallback hierarchy: BSI -> BDI -> first available
        for index in ['BSI', 'BDI', 'BPI', 'BCI', 'BHSI']:
            if index in self.baltic.columns:
                return self.baltic[index].iloc[-1]['value']

        # If no Baltic indices available, use a default rate
        print(f"[WARNING] No Baltic indices available, using default rate")
        return 8000  # Default daily rate in USD

    def _map_region(self, port):
        """Map port to regional factor category"""
        # China ports
        if port in ['Tianjin', 'Rizhao', 'Shanghai', 'Qingdao', 'Dalian', 'Ningbo']:
            return 'China'
        # Russia ports
        elif port in ['Novorossiysk']:
            return 'Russia'
        # Turkey ports
        elif port in ['Iskenderun', 'Mersin', 'Nemrut Bay', 'Izmir', 'Aliaga']:
            return 'Turkey'
        # Portugal ports
        elif port in ['Sines', 'Leixões']:
            return 'Portugal'
        # Mediterranean ports (Spain, Italy, France)
        elif port in ['Valencia', 'Barcelona', 'Bilbao', 'Genoa', 'La Spezia', 'Marseille']:
            return 'Mediterranean'
        # Northern Europe ports (Germany, Netherlands, Belgium, Northern France)
        elif port in ['Hamburg', 'Bremen', 'Rotterdam', 'Amsterdam', 'Antwerp', 'Le Havre']:
            return 'Northern_Europe'
        # Eastern Europe ports (Poland, Romania, Bulgaria)
        elif port in ['Gdansk', 'Gdynia', 'Constanta', 'Varna']:
            return 'Eastern_Europe'
        else:
            return 'default'

# Implementation
if __name__ == "__main__":
    print("[INFO] Starting Enhanced Shipping Rate Calculation System...")
    print("=" * 70)

    integrator = ShippingDataIntegrator()

    # Load all required indices
    print("\n[STEP 1] Loading market indices...")
    drewry_data = integrator.load_drewry_indices()
    baltic_data = integrator.load_baltic_indices()

    print(f"[INFO] Drewry DataFrame columns: {drewry_data.columns}")
    print(f"[INFO] Baltic DataFrame columns: {baltic_data.columns}")

    # Initialize calculator
    print("\n[STEP 2] Initializing cost calculation engine...")
    calculator = BreakbulkCostEngine(drewry_data, baltic_data)

    # Define expanded port list (prioritize known ports with voyage times)
    print("\n[STEP 3] Calculating rates for all port combinations...")
    priority_ports = ["Tianjin", "Rizhao", "Shanghai", "Novorossiysk",
                     "Iskenderun", "Mersin", "Nemrut Bay", "Sines", "Leixões"]

    # Additional ports (if you want to include them)
    additional_ports = ["Qingdao", "Dalian", "Ningbo", "Izmir", "Valencia",
                       "Barcelona", "Genoa", "Rotterdam", "Hamburg", "Antwerp"]

    # Use priority ports for main calculation
    origin_ports = priority_ports
    cargo_types = ['General_Cargo', 'Project_Cargo']
    results = []

    for port in origin_ports:
        for cargo_type in cargo_types:
            try:
                print(f"[INFO] Calculating rate for {port} - {cargo_type}")
                rate = calculator.calculate_route_cost(port, cargo_type)
                country = calculator.port_to_country.get(port, "Unknown")
                print(f"[RESULT] {port} ({country}) - {cargo_type}: {rate} USD/ton")
                results.append({
                    'Origin_Country': country,
                    'Origin_Port': port,
                    'Cargo_Type': cargo_type,
                    'Rate_USD_per_ton': rate,
                    'Calculation_Date': datetime.now().date()
                })
            except Exception as e:
                print(f"[ERROR] Failed to calculate rate for {port} - {cargo_type}: {e}")
                continue
    # Generate comprehensive report and visualizations
    print(f"\n[STEP 4] Generating comprehensive reports...")
    report = pd.DataFrame(results)

    if not report.empty:
        # Enhanced visualization
        report['Port_Label'] = report['Origin_Port'] + ' (' + report['Origin_Country'] + ')'

        # Create multiple visualizations
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 12))

        # 1. Main comparison chart
        sns.barplot(data=report, x='Port_Label', y='Rate_USD_per_ton', hue='Cargo_Type', ax=ax1)
        ax1.set_title('Breakbulk Shipping Rates to Israel (General vs Project Cargo)', fontsize=14)
        ax1.set_ylabel('USD per Metric Ton')
        ax1.tick_params(axis='x', rotation=45)

        # 2. Country-wise average rates
        country_avg = report.groupby(['Origin_Country', 'Cargo_Type'])['Rate_USD_per_ton'].mean().reset_index()
        sns.barplot(data=country_avg, x='Origin_Country', y='Rate_USD_per_ton', hue='Cargo_Type', ax=ax2)
        ax2.set_title('Average Rates by Country', fontsize=14)
        ax2.set_ylabel('USD per Metric Ton')

        # 3. Rate distribution
        sns.boxplot(data=report, x='Cargo_Type', y='Rate_USD_per_ton', ax=ax3)
        ax3.set_title('Rate Distribution by Cargo Type', fontsize=14)
        ax3.set_ylabel('USD per Metric Ton')

        # 4. Top 10 most competitive routes
        top_routes = report.nsmallest(10, 'Rate_USD_per_ton')
        sns.barplot(data=top_routes, x='Port_Label', y='Rate_USD_per_ton', hue='Cargo_Type', ax=ax4)
        ax4.set_title('Top 10 Most Competitive Routes', fontsize=14)
        ax4.set_ylabel('USD per Metric Ton')
        ax4.tick_params(axis='x', rotation=45)

        plt.tight_layout()

        # Save outputs
        output_dir = Path(r"C:\Users\<USER>\Code\shipment\reports")
        output_dir.mkdir(exist_ok=True)

        # Enhanced CSV report with additional metrics
        report_enhanced = report.copy()
        report_enhanced['Voyage_Days'] = report_enhanced['Origin_Port'].map(calculator.voyage_days_map)
        report_enhanced['Region'] = report_enhanced['Origin_Port'].map(calculator._map_region)

        # Save files
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_enhanced.to_csv(output_dir / f"enhanced_breakbulk_cost_report_{timestamp}.csv", index=False)
        plt.savefig(output_dir / f"comprehensive_rates_analysis_{timestamp}.png", dpi=300, bbox_inches='tight')

        # Print summary statistics
        print(f"\n[SUMMARY] Analysis completed successfully!")
        print(f"Total routes analyzed: {len(report)}")
        print(f"Countries covered: {report['Origin_Country'].nunique()}")
        print(f"Ports covered: {report['Origin_Port'].nunique()}")
        print(f"\nRate Statistics (USD/ton):")
        print(f"  Minimum rate: {report['Rate_USD_per_ton'].min():.2f}")
        print(f"  Maximum rate: {report['Rate_USD_per_ton'].max():.2f}")
        print(f"  Average rate: {report['Rate_USD_per_ton'].mean():.2f}")
        print(f"\nMost competitive routes:")
        for _, row in report.nsmallest(5, 'Rate_USD_per_ton').iterrows():
            print(f"  {row['Origin_Port']} ({row['Origin_Country']}) - {row['Cargo_Type']}: {row['Rate_USD_per_ton']:.2f} USD/ton")

        print(f"\nReports saved to: {output_dir}")
        plt.show()
    else:
        print("[ERROR] No results generated. Check data availability and port configurations.")