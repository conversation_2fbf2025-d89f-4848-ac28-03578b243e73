#!/usr/bin/env python3
"""
🏗️ REBAR SHIPPING CALCULATOR - Simple UI
Web-based interface for calculating rebar CIF prices to Israel
"""

import sys
from pathlib import Path
import threading
import webbrowser
from datetime import datetime

# Add scrapers to path
sys.path.append(str(Path(__file__).parent / "scrapers"))

try:
    from flask import Flask, render_template, request, jsonify, send_from_directory
    from rebar_calculator import RebarShippingCalculator
except ImportError:
    print("❌ Missing dependencies. Please install:")
    print("pip install flask pandas numpy requests pathlib")
    sys.exit(1)

app = Flask(__name__)

# Initialize calculator
calculator = None

def init_calculator():
    """Initialize the rebar calculator"""
    global calculator
    try:
        calculator = RebarShippingCalculator()
        print("✅ Calculator initialized successfully")
        return True
    except Exception as e:
        print(f"❌ Failed to initialize calculator: {e}")
        return False

@app.route('/')
def index():
    """Main page"""
    return render_template('index.html')

@app.route('/api/calculate', methods=['POST'])
def calculate():
    """Calculate CIF price for rebar shipment"""
    try:
        data = request.get_json()
        
        port = data.get('port')
        tonnage = int(data.get('tonnage', 1000))
        grade = data.get('grade', 'C-500')
        
        if not calculator:
            return jsonify({'error': 'Calculator not initialized'}), 500
        
        result = calculator.calculate_rebar_cif_price(port, tonnage, grade)
        
        return jsonify({
            'success': True,
            'result': result
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 400

@app.route('/api/compare', methods=['POST'])
def compare():
    """Compare CIF prices across all ports"""
    try:
        data = request.get_json()
        
        tonnage = int(data.get('tonnage', 1000))
        grade = data.get('grade', 'C-500')
        
        if not calculator:
            return jsonify({'error': 'Calculator not initialized'}), 500
        
        comparison = calculator.compare_rebar_routes(tonnage, grade)
        
        if comparison.empty:
            return jsonify({'error': 'No data available'}), 400
        
        # Add freight percentage
        comparison['Freight_Pct'] = (comparison['Freight_USD_per_ton'] / comparison['FOB_USD_per_ton'] * 100).round(1)
        
        # Convert to list of dictionaries
        results = comparison.to_dict('records')
        
        return jsonify({
            'success': True,
            'results': results
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 400

@app.route('/api/ports')
def get_ports():
    """Get available ports"""
    try:
        if not calculator:
            return jsonify({'error': 'Calculator not initialized'}), 500
        
        ports = []
        for port_name, port_info in calculator.rebar_ports.items():
            ports.append({
                'name': port_name,
                'country': port_info['country'],
                'voyage_days': port_info['voyage_days'],
                'steel_grades': port_info['steel_grade_available'],
                'typical_tonnage': port_info['typical_rebar_tonnage']
            })
        
        return jsonify({
            'success': True,
            'ports': ports
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 400

@app.route('/api/scrapers/<scraper_name>', methods=['POST'])
def run_scraper(scraper_name):
    """Run data scrapers"""
    try:
        if scraper_name == 'lme':
            from lme_api_extractor import LMEAPIExtractor
            extractor = LMEAPIExtractor()
            threading.Thread(target=extractor.extract_api_data).start()
            return jsonify({'success': True, 'message': 'LME scraper started'})
            
        elif scraper_name == 'drewry':
            from drewry_breakbulk_data_extractor import DrewryBreakbulkExtractor
            extractor = DrewryBreakbulkExtractor()
            threading.Thread(target=extractor.extract_all_data).start()
            return jsonify({'success': True, 'message': 'Drewry scraper started'})
            
        elif scraper_name == 'kline':
            from kline_complete_data_extractor import KlineDataExtractor
            extractor = KlineDataExtractor()
            threading.Thread(target=extractor.extract_all_data).start()
            return jsonify({'success': True, 'message': 'Kline scraper started'})
            
        else:
            return jsonify({'error': 'Unknown scraper'}), 400
            
    except Exception as e:
        return jsonify({'error': str(e)}), 400

# Create templates directory and HTML template
def create_templates():
    """Create the HTML template"""
    templates_dir = Path('templates')
    templates_dir.mkdir(exist_ok=True)
    
    html_content = '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏗️ Rebar Shipping Calculator</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .section { margin-bottom: 30px; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select, button { padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        button { background-color: #007bff; color: white; cursor: pointer; }
        button:hover { background-color: #0056b3; }
        .results { margin-top: 20px; }
        .result-item { padding: 10px; margin: 5px 0; background-color: #f8f9fa; border-radius: 5px; }
        .best-option { background-color: #d4edda; border: 1px solid #c3e6cb; }
        table { width: 100%; border-collapse: collapse; margin-top: 10px; }
        th, td { padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f8f9fa; }
        .scraper-buttons { display: flex; gap: 10px; margin-top: 10px; }
        .scraper-btn { background-color: #28a745; }
        .scraper-btn:hover { background-color: #218838; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏗️ Rebar Shipping Calculator for Israel</h1>
            <p>Calculate CIF prices: FOB (LME) + Freight (Baltic) + Insurance</p>
        </div>

        <div class="section">
            <h2>📊 Calculate CIF Price</h2>
            <div class="form-group">
                <label for="port">Origin Port:</label>
                <select id="port">
                    <option value="">Select Port...</option>
                </select>
            </div>
            <div class="form-group">
                <label for="tonnage">Tonnage (tons):</label>
                <input type="number" id="tonnage" value="5000" min="1" max="50000">
            </div>
            <div class="form-group">
                <label for="grade">Steel Grade:</label>
                <select id="grade">
                    <option value="C-500">C-500</option>
                    <option value="B-500">B-500</option>
                </select>
            </div>
            <button onclick="calculateSingle()">Calculate CIF Price</button>
            <button onclick="compareAll()">Compare All Ports</button>
            
            <div id="results" class="results"></div>
        </div>

        <div class="section">
            <h2>🔄 Data Scrapers</h2>
            <p>Update market data from external sources:</p>
            <div class="scraper-buttons">
                <button class="scraper-btn" onclick="runScraper('lme')">🏗️ LME Steel Prices</button>
                <button class="scraper-btn" onclick="runScraper('drewry')">🚢 Drewry Indices</button>
                <button class="scraper-btn" onclick="runScraper('kline')">📊 Kline Data</button>
            </div>
            <div id="scraper-status"></div>
        </div>
    </div>

    <script>
        // Load ports on page load
        window.onload = function() {
            loadPorts();
        };

        function loadPorts() {
            fetch('/api/ports')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const portSelect = document.getElementById('port');
                        data.ports.forEach(port => {
                            const option = document.createElement('option');
                            option.value = port.name;
                            option.textContent = `${port.name} (${port.country}) - ${port.voyage_days} days`;
                            portSelect.appendChild(option);
                        });
                    }
                })
                .catch(error => console.error('Error loading ports:', error));
        }

        function calculateSingle() {
            const port = document.getElementById('port').value;
            const tonnage = document.getElementById('tonnage').value;
            const grade = document.getElementById('grade').value;

            if (!port) {
                alert('Please select a port');
                return;
            }

            fetch('/api/calculate', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ port, tonnage, grade })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displaySingleResult(data.result);
                } else {
                    alert('Error: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Calculation failed');
            });
        }

        function compareAll() {
            const tonnage = document.getElementById('tonnage').value;
            const grade = document.getElementById('grade').value;

            fetch('/api/compare', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ tonnage, grade })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayComparisonResults(data.results);
                } else {
                    alert('Error: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Comparison failed');
            });
        }

        function displaySingleResult(result) {
            const resultsDiv = document.getElementById('results');
            const cif = result.cif_breakdown;
            const totals = result.total_costs;
            
            resultsDiv.innerHTML = `
                <h3>💰 CIF Price Breakdown</h3>
                <div class="result-item best-option">
                    <strong>${result.shipment_details.origin_port} (${result.shipment_details.country})</strong><br>
                    FOB Price: $${cif.fob_price_per_ton}/ton<br>
                    Freight Cost: $${cif.freight_cost_per_ton}/ton<br>
                    Insurance: $${cif.insurance_cost_per_ton}/ton<br>
                    <strong>CIF Total: $${cif.cif_price_per_ton}/ton</strong><br>
                    <strong>Total Cost: $${totals.cif_total.toLocaleString()}</strong>
                </div>
            `;
        }

        function displayComparisonResults(results) {
            const resultsDiv = document.getElementById('results');
            
            let html = '<h3>🏆 Route Comparison</h3>';
            html += '<table><thead><tr>';
            html += '<th>Rank</th><th>Port</th><th>Country</th><th>FOB</th><th>Freight</th><th>Frt%</th><th>CIF</th><th>Total</th>';
            html += '</tr></thead><tbody>';
            
            results.forEach((result, index) => {
                const rowClass = index === 0 ? 'best-option' : '';
                html += `<tr class="${rowClass}">`;
                html += `<td>${index + 1}</td>`;
                html += `<td>${result.Origin_Port}</td>`;
                html += `<td>${result.Country}</td>`;
                html += `<td>$${Math.round(result.FOB_USD_per_ton)}</td>`;
                html += `<td>$${Math.round(result.Freight_USD_per_ton)}</td>`;
                html += `<td>${result.Freight_Pct}%</td>`;
                html += `<td>$${Math.round(result.CIF_USD_per_ton)}</td>`;
                html += `<td>$${Math.round(result.Total_CIF_USD).toLocaleString()}</td>`;
                html += '</tr>';
            });
            
            html += '</tbody></table>';
            resultsDiv.innerHTML = html;
        }

        function runScraper(scraperName) {
            const statusDiv = document.getElementById('scraper-status');
            statusDiv.innerHTML = `<p>🔄 Running ${scraperName.toUpperCase()} scraper...</p>`;
            
            fetch(`/api/scrapers/${scraperName}`, { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        statusDiv.innerHTML = `<p>✅ ${data.message}</p>`;
                    } else {
                        statusDiv.innerHTML = `<p>❌ Error: ${data.error}</p>`;
                    }
                })
                .catch(error => {
                    statusDiv.innerHTML = `<p>❌ Scraper failed: ${error}</p>`;
                });
        }
    </script>
</body>
</html>'''
    
    with open(templates_dir / 'index.html', 'w') as f:
        f.write(html_content)

def main():
    """Main function to run the UI application"""
    print("🏗️ REBAR SHIPPING CALCULATOR - UI")
    print("=" * 50)
    
    # Create templates
    create_templates()
    
    # Initialize calculator
    print("🔧 Initializing calculator...")
    if not init_calculator():
        print("❌ Failed to start application")
        return
    
    # Start Flask app
    print("🚀 Starting web interface...")
    print("📱 Open your browser to: http://localhost:5000")
    
    # Auto-open browser
    threading.Timer(1.5, lambda: webbrowser.open('http://localhost:5000')).start()
    
    try:
        app.run(debug=False, host='0.0.0.0', port=5000)
    except KeyboardInterrupt:
        print("\n👋 Shutting down...")

if __name__ == "__main__":
    main()
