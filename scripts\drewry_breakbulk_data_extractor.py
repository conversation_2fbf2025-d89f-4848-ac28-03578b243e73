from playwright.sync_api import sync_playwright
import json
import csv
import os
from datetime import datetime
from pathlib import Path
import re

class DrewryBreakbulkDataExtractor:
    def __init__(self):
        self.base_url = 'https://www.drewry.co.uk/maritime-research/maritime-research-related-content/breakbulk-sea-transport-indices'
        self.output_dir = Path('data')
        self.charts_dir = self.output_dir / 'drewry_charts_data'
        self.indices_dir = self.output_dir / 'breakbulk_indices'
        self.raw_dir = self.output_dir / 'drewry_raw_extracts'
        
        # Create directories
        for dir_path in [self.output_dir, self.charts_dir, self.indices_dir, self.raw_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
    
    def extract_all_data(self):
        """
        Extract all chart data from the Drewry Breakbulk Sea Transport Indices page
        """
        print("🚢 Drewry Breakbulk Sea Transport Indices Data Extractor")
        print("=" * 60)
        
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=False)
            context = browser.new_context()
            page = context.new_page()
            
            print(f"📡 Loading page: {self.base_url}")
            page.goto(self.base_url, wait_until='networkidle')
            page.wait_for_timeout(8000)  # Wait longer for charts to load
            
            # Try multiple chart extraction methods
            charts_data = []
            
            # Method 1: Extract from Highcharts runtime
            highcharts_data = self._extract_highcharts_data(page)
            if highcharts_data:
                charts_data.extend(highcharts_data)
            
            # Method 2: Extract from Chart.js if present
            chartjs_data = self._extract_chartjs_data(page)
            if chartjs_data:
                charts_data.extend(chartjs_data)
            
            # Method 3: Extract from D3.js or other chart libraries
            d3_data = self._extract_d3_data(page)
            if d3_data:
                charts_data.extend(d3_data)
            
            # Method 4: Look for embedded data in script tags
            script_data = self._extract_script_data(page)
            if script_data:
                charts_data.extend(script_data)

            # Method 5: Extract from ChartBlocks iframe
            chartblocks_data = self._extract_chartblocks_data(page)
            if chartblocks_data:
                charts_data.extend(chartblocks_data)

            # Save raw chart data
            raw_file = self.raw_dir / 'drewry_breakbulk_charts_data.json'
            with open(raw_file, 'w') as f:
                json.dump(charts_data, f, indent=2)
            print(f"💾 Saved raw chart data: {raw_file}")

            # Process and organize data
            if charts_data:
                # Check if we have ChartBlocks data format
                if any('series' in chart and isinstance(chart['series'], dict) for chart in charts_data):
                    self._process_chartblocks_data(charts_data)
                else:
                    self._process_all_charts(charts_data)
            else:
                print("⚠️  No chart data found, trying alternative extraction methods...")
                self._extract_alternative_data(page)
            
            # Extract any table data
            self._extract_table_data(page)
            
            browser.close()
        
        print("\n✅ Data extraction complete!")
        self._generate_summary()
    
    def _extract_highcharts_data(self, page):
        """Extract data from Highcharts instances"""
        print("📊 Extracting Highcharts data...")
        
        try:
            charts_data = page.evaluate('''
                () => {
                    if (typeof Highcharts !== 'undefined' && Highcharts.charts) {
                        return Highcharts.charts.map((chart, index) => {
                            if (chart && chart.series) {
                                return {
                                    index: index,
                                    title: chart.title ? chart.title.textStr : `Drewry Chart ${index}`,
                                    subtitle: chart.subtitle ? chart.subtitle.textStr : '',
                                    yAxis: chart.yAxis ? chart.yAxis.map(axis => ({
                                        title: axis.options.title ? axis.options.title.text : '',
                                        min: axis.min,
                                        max: axis.max
                                    })) : [],
                                    xAxis: chart.xAxis ? chart.xAxis.map(axis => ({
                                        title: axis.options.title ? axis.options.title.text : '',
                                        categories: axis.categories || [],
                                        type: axis.options.type || 'linear'
                                    })) : [],
                                    series: chart.series.map(s => ({
                                        name: s.name,
                                        type: s.type,
                                        color: s.color,
                                        yAxis: s.yAxis ? s.yAxis.options.index : 0,
                                        data: s.data ? s.data.map(point => {
                                            if (point && typeof point === 'object') {
                                                return {
                                                    x: point.x !== undefined ? point.x : point.index,
                                                    y: point.y,
                                                    category: point.category || point.name || null,
                                                    name: point.name || null
                                                };
                                            }
                                            return { x: null, y: point, category: null, name: null };
                                        }) : []
                                    }))
                                };
                            }
                            return null;
                        }).filter(chart => chart !== null);
                    }
                    return [];
                }
            ''')
            
            if charts_data:
                print(f"📈 Found {len(charts_data)} Highcharts")
            return charts_data
            
        except Exception as e:
            print(f"❌ Error extracting Highcharts data: {e}")
            return []
    
    def _extract_chartjs_data(self, page):
        """Extract data from Chart.js instances"""
        print("📊 Extracting Chart.js data...")
        
        try:
            charts_data = page.evaluate('''
                () => {
                    if (typeof Chart !== 'undefined' && Chart.instances) {
                        return Object.values(Chart.instances).map((chart, index) => {
                            if (chart && chart.data) {
                                return {
                                    index: index,
                                    title: chart.options?.plugins?.title?.text || `Drewry Chart.js ${index}`,
                                    subtitle: chart.options?.plugins?.subtitle?.text || '',
                                    type: chart.config.type,
                                    data: chart.data.datasets.map(dataset => ({
                                        name: dataset.label,
                                        type: dataset.type || chart.config.type,
                                        color: dataset.backgroundColor || dataset.borderColor,
                                        data: dataset.data.map((value, idx) => ({
                                            x: idx,
                                            y: value,
                                            category: chart.data.labels ? chart.data.labels[idx] : null,
                                            name: null
                                        }))
                                    }))
                                };
                            }
                            return null;
                        }).filter(chart => chart !== null);
                    }
                    return [];
                }
            ''')
            
            if charts_data:
                print(f"📈 Found {len(charts_data)} Chart.js instances")
            return charts_data
            
        except Exception as e:
            print(f"❌ Error extracting Chart.js data: {e}")
            return []
    
    def _extract_d3_data(self, page):
        """Extract data from D3.js visualizations"""
        print("📊 Extracting D3.js data...")
        
        try:
            # Look for common D3 data patterns
            d3_data = page.evaluate('''
                () => {
                    const charts = [];
                    
                    // Look for SVG elements that might contain D3 charts
                    const svgs = document.querySelectorAll('svg');
                    svgs.forEach((svg, index) => {
                        const paths = svg.querySelectorAll('path');
                        const circles = svg.querySelectorAll('circle');
                        const rects = svg.querySelectorAll('rect');
                        
                        if (paths.length > 0 || circles.length > 0 || rects.length > 0) {
                            charts.push({
                                index: index,
                                title: `D3 Chart ${index}`,
                                subtitle: '',
                                type: 'svg',
                                elements: {
                                    paths: paths.length,
                                    circles: circles.length,
                                    rects: rects.length
                                }
                            });
                        }
                    });
                    
                    return charts;
                }
            ''')
            
            if d3_data:
                print(f"📈 Found {len(d3_data)} potential D3.js visualizations")
            return d3_data
            
        except Exception as e:
            print(f"❌ Error extracting D3.js data: {e}")
            return []
    
    def _extract_script_data(self, page):
        """Extract data from script tags that might contain chart data"""
        print("📊 Extracting data from script tags...")
        
        try:
            script_data = page.evaluate('''
                () => {
                    const scripts = document.querySelectorAll('script');
                    const dataScripts = [];
                    
                    scripts.forEach((script, index) => {
                        const content = script.textContent || script.innerHTML;
                        
                        // Look for common data patterns
                        if (content.includes('data') && 
                            (content.includes('[') || content.includes('{')) &&
                            (content.includes('chart') || content.includes('series') || 
                             content.includes('breakbulk') || content.includes('index'))) {
                            
                            dataScripts.push({
                                index: index,
                                title: `Script Data ${index}`,
                                content: content.substring(0, 1000), // First 1000 chars
                                hasArrays: content.includes('['),
                                hasObjects: content.includes('{'),
                                hasNumbers: /\\d+\\.?\\d*/.test(content)
                            });
                        }
                    });
                    
                    return dataScripts;
                }
            ''')
            
            if script_data:
                print(f"📈 Found {len(script_data)} scripts with potential data")
            return script_data
            
        except Exception as e:
            print(f"❌ Error extracting script data: {e}")
            return []

    def _extract_chartblocks_data(self, page):
        """Extract data from ChartBlocks iframe"""
        print("📊 Extracting ChartBlocks data...")

        try:
            # Find ChartBlocks iframe
            chartblocks_iframe = page.locator('iframe[src*="chartblocks.com"]').first

            if chartblocks_iframe.count() > 0:
                iframe_src = chartblocks_iframe.get_attribute('src')
                print(f"🔗 Found ChartBlocks iframe: {iframe_src}")

                # Fix protocol if missing
                if iframe_src.startswith('//'):
                    iframe_src = 'https:' + iframe_src

                # Navigate to the iframe content
                iframe_page = page.context.new_page()
                try:
                    iframe_page.goto(iframe_src, wait_until='networkidle', timeout=60000)
                    iframe_page.wait_for_timeout(8000)  # Wait for chart to load
                except Exception as e:
                    print(f"⚠️  Timeout loading iframe, trying with domcontentloaded: {e}")
                    try:
                        iframe_page.goto(iframe_src, wait_until='domcontentloaded', timeout=30000)
                        iframe_page.wait_for_timeout(5000)
                    except Exception as e2:
                        print(f"❌ Could not load iframe: {e2}")
                        iframe_page.close()
                        return []

                # Try to extract data from the iframe
                chart_data = []

                # Method 1: Try to get data from ChartBlocks API or embedded data
                try:
                    cb_data = iframe_page.evaluate('''
                        () => {
                            // Look for ChartBlocks specific data structures
                            if (window.chartData || window.data) {
                                return window.chartData || window.data;
                            }

                            // Look for Highcharts in iframe
                            if (typeof Highcharts !== 'undefined' && Highcharts.charts) {
                                return Highcharts.charts.map((chart, index) => {
                                    if (chart && chart.series) {
                                        return {
                                            index: index,
                                            title: chart.title ? chart.title.textStr : 'Breakbulk Sea Transport Indices',
                                            subtitle: chart.subtitle ? chart.subtitle.textStr : '',
                                            series: chart.series.map(s => ({
                                                name: s.name || 'Index',
                                                type: s.type,
                                                color: s.color,
                                                data: s.data ? s.data.map(point => {
                                                    if (point && typeof point === 'object') {
                                                        return {
                                                            x: point.x !== undefined ? point.x : point.index,
                                                            y: point.y,
                                                            category: point.category || point.name || null,
                                                            name: point.name || null
                                                        };
                                                    }
                                                    return { x: null, y: point, category: null, name: null };
                                                }) : []
                                            }))
                                        };
                                    }
                                    return null;
                                }).filter(chart => chart !== null);
                            }

                            // Look for any global data variables
                            const possibleDataVars = ['data', 'chartData', 'seriesData', 'indexData'];
                            for (let varName of possibleDataVars) {
                                if (window[varName]) {
                                    return [{
                                        index: 0,
                                        title: 'Breakbulk Sea Transport Indices',
                                        subtitle: '',
                                        data: window[varName]
                                    }];
                                }
                            }

                            return null;
                        }
                    ''')

                    if cb_data:
                        chart_data.extend(cb_data if isinstance(cb_data, list) else [cb_data])
                        print(f"📈 Extracted data from ChartBlocks iframe")

                except Exception as e:
                    print(f"⚠️  Could not extract data from iframe JavaScript: {e}")

                # Method 2: Try to extract data from DOM elements
                try:
                    dom_data = iframe_page.evaluate('''
                        () => {
                            const dataPoints = [];

                            // Look for SVG elements with data
                            const svgElements = document.querySelectorAll('svg');
                            svgElements.forEach(svg => {
                                const paths = svg.querySelectorAll('path');
                                const circles = svg.querySelectorAll('circle');
                                const rects = svg.querySelectorAll('rect');

                                if (paths.length > 0 || circles.length > 0) {
                                    dataPoints.push({
                                        type: 'svg',
                                        elements: paths.length + circles.length + rects.length
                                    });
                                }
                            });

                            // Look for text elements that might contain data
                            const textElements = document.querySelectorAll('text, tspan');
                            const numbers = [];
                            textElements.forEach(el => {
                                const text = el.textContent.trim();
                                const num = parseFloat(text);
                                if (!isNaN(num) && num > 0 && num < 1000) {
                                    numbers.push(num);
                                }
                            });

                            if (numbers.length > 0) {
                                return [{
                                    index: 0,
                                    title: 'Breakbulk Sea Transport Indices',
                                    subtitle: 'Extracted from DOM',
                                    series: [{
                                        name: 'Index Values',
                                        type: 'line',
                                        data: numbers.map((val, idx) => ({
                                            x: idx,
                                            y: val,
                                            category: `Point ${idx + 1}`,
                                            name: null
                                        }))
                                    }]
                                }];
                            }

                            return [];
                        }
                    ''')

                    if dom_data:
                        chart_data.extend(dom_data)
                        print(f"📈 Extracted {len(dom_data)} charts from DOM elements")

                except Exception as e:
                    print(f"⚠️  Could not extract data from iframe DOM: {e}")

                iframe_page.close()
                return chart_data

            return []

        except Exception as e:
            print(f"❌ Error extracting ChartBlocks data: {e}")
            return []
    
    def _extract_alternative_data(self, page):
        """Alternative data extraction methods when standard chart libraries aren't found"""
        print("🔍 Trying alternative data extraction methods...")
        
        try:
            # Look for canvas elements
            canvas_info = page.evaluate('''
                () => {
                    const canvases = document.querySelectorAll('canvas');
                    return Array.from(canvases).map((canvas, index) => ({
                        index: index,
                        id: canvas.id,
                        className: canvas.className,
                        width: canvas.width,
                        height: canvas.height
                    }));
                }
            ''')
            
            if canvas_info:
                print(f"🎨 Found {len(canvas_info)} canvas elements")
                
                # Save canvas info
                canvas_file = self.raw_dir / 'canvas_elements.json'
                with open(canvas_file, 'w') as f:
                    json.dump(canvas_info, f, indent=2)
            
            # Look for iframe elements that might contain charts
            iframe_info = page.evaluate('''
                () => {
                    const iframes = document.querySelectorAll('iframe');
                    return Array.from(iframes).map((iframe, index) => ({
                        index: index,
                        src: iframe.src,
                        id: iframe.id,
                        className: iframe.className
                    }));
                }
            ''')
            
            if iframe_info:
                print(f"🖼️  Found {len(iframe_info)} iframe elements")
                
                # Save iframe info
                iframe_file = self.raw_dir / 'iframe_elements.json'
                with open(iframe_file, 'w') as f:
                    json.dump(iframe_info, f, indent=2)
            
        except Exception as e:
            print(f"❌ Error in alternative extraction: {e}")

    def _process_chartblocks_data(self, charts_data):
        """Process ChartBlocks specific data format"""
        print("\n🔄 Processing ChartBlocks data...")

        chart_summary = []

        for chart_idx, chart in enumerate(charts_data):
            if 'series' in chart and isinstance(chart['series'], dict):
                print(f"\n📊 Processing ChartBlocks chart {chart_idx}")

                # Create chart-specific folder
                chart_folder = self.charts_dir / f"chartblocks_{chart_idx:02d}_breakbulk_indices"
                chart_folder.mkdir(exist_ok=True)

                series_info = []
                data_files = []

                # Process each series in the ChartBlocks format
                for series_id, series_data in chart['series'].items():
                    if 'values' in series_data and series_data['values']:
                        # Determine series name based on ID
                        if series_id == 'ts-375':
                            series_name = 'General_Cargo_Index'
                        elif series_id == 'ts-899':
                            series_name = 'Project_Cargo_Index'
                        else:
                            series_name = f'Series_{series_id}'

                        print(f"   📈 Processing {series_name}: {len(series_data['values'])} data points")

                        # Extract values and dates
                        values = series_data['values']
                        raw_dates = series_data.get('raw', [])

                        # Create CSV file
                        csv_filename = f"{series_name}_data.csv"
                        csv_file = chart_folder / csv_filename

                        with open(csv_file, 'w', newline='') as f:
                            writer = csv.DictWriter(f, fieldnames=['date', 'value', 'timestamp'])
                            writer.writeheader()

                            for i, point in enumerate(values):
                                date_label = raw_dates[i]['x'] if i < len(raw_dates) else f"Point_{i+1}"
                                writer.writerow({
                                    'date': date_label,
                                    'value': point['y'],
                                    'timestamp': point['x']
                                })

                        data_files.append(csv_filename)
                        series_info.append({
                            'name': series_name,
                            'id': series_id,
                            'points': len(values)
                        })

                        # Copy to main indices folder
                        main_file = self.indices_dir / csv_filename
                        with open(main_file, 'w', newline='') as f:
                            writer = csv.DictWriter(f, fieldnames=['date', 'value'])
                            writer.writeheader()
                            for i, point in enumerate(values):
                                date_label = raw_dates[i]['x'] if i < len(raw_dates) else f"Point_{i+1}"
                                writer.writerow({
                                    'date': date_label,
                                    'value': point['y']
                                })

                        print(f"   💾 Saved {series_name} → {csv_filename}")
                        print(f"   📋 Copied to main indices: {csv_filename}")

                # Save chart metadata
                metadata_file = chart_folder / 'chart_metadata.json'
                with open(metadata_file, 'w') as f:
                    json.dump({
                        'title': 'Drewry Breakbulk Sea Transport Indices',
                        'subtitle': 'ChartBlocks Data',
                        'type': 'chartblocks',
                        'source': 'https://embed.chartblocks.com',
                        'series_info': series_info
                    }, f, indent=2)

                chart_summary.append({
                    'index': chart_idx,
                    'title': 'Drewry Breakbulk Sea Transport Indices',
                    'type': 'chartblocks',
                    'series_count': len(series_info),
                    'data_files': data_files
                })

        # Save overall summary
        summary_file = self.charts_dir / 'chartblocks_summary.json'
        with open(summary_file, 'w') as f:
            json.dump(chart_summary, f, indent=2)

        return chart_summary

    def _process_all_charts(self, charts_data):
        """Process and save data from all charts"""
        print("\n🔄 Processing chart data...")

        chart_summary = []

        for i, chart in enumerate(charts_data):
            chart_info = {
                'index': chart.get('index', i),
                'title': chart.get('title', f'Chart {i}'),
                'subtitle': chart.get('subtitle', ''),
                'type': chart.get('type', 'unknown'),
                'series_count': len(chart.get('series', chart.get('data', []))),
                'data_files': []
            }

            print(f"\n📊 Chart {chart_info['index']}: {chart_info['title']}")
            if chart_info.get('subtitle'):
                print(f"   Subtitle: {chart_info['subtitle']}")

            # Create chart-specific folder
            chart_folder = self.charts_dir / f"chart_{chart_info['index']:02d}_{self._sanitize_filename(chart_info['title'])}"
            chart_folder.mkdir(exist_ok=True)

            # Save chart metadata
            metadata_file = chart_folder / 'chart_metadata.json'
            with open(metadata_file, 'w') as f:
                json.dump({
                    'title': chart_info['title'],
                    'subtitle': chart_info.get('subtitle', ''),
                    'type': chart_info.get('type', 'unknown'),
                    'yAxis': chart.get('yAxis', []),
                    'xAxis': chart.get('xAxis', []),
                    'series_info': [{'name': s.get('name', f'Series {i}'),
                                   'type': s.get('type', 'unknown'),
                                   'color': s.get('color', '')}
                                   for i, s in enumerate(chart.get('series', chart.get('data', [])))]
                }, f, indent=2)

            # Process each series/dataset
            series_data = chart.get('series', chart.get('data', []))
            for series in series_data:
                if series.get('data'):
                    # Filter out null values
                    valid_data = [point for point in series['data'] if point.get('y') is not None]

                    if valid_data:
                        # Save series data
                        series_name = series.get('name', f'Series_{chart_info["index"]}')
                        series_filename = f"{self._sanitize_filename(series_name)}_data.csv"
                        series_file = chart_folder / series_filename

                        with open(series_file, 'w', newline='') as f:
                            writer = csv.DictWriter(f, fieldnames=['date', 'value', 'x_index', 'category'])
                            writer.writeheader()

                            for point in valid_data:
                                writer.writerow({
                                    'date': point.get('category') or f"Point_{point.get('x', 0)}",
                                    'value': point.get('y'),
                                    'x_index': point.get('x'),
                                    'category': point.get('category')
                                })

                        chart_info['data_files'].append(series_filename)
                        print(f"   💾 {series_name}: {len(valid_data)} data points → {series_filename}")

                        # Copy breakbulk indices to main indices folder
                        if self._is_breakbulk_index(series_name, chart_info['title']):
                            main_file = self.indices_dir / series_filename
                            with open(main_file, 'w', newline='') as f:
                                writer = csv.DictWriter(f, fieldnames=['date', 'value'])
                                writer.writeheader()
                                for point in valid_data:
                                    writer.writerow({
                                        'date': point.get('category') or f"Point_{point.get('x', 0)}",
                                        'value': point.get('y')
                                    })
                            print(f"   📋 Copied to main indices: {series_filename}")

            chart_summary.append(chart_info)

        # Save overall summary
        summary_file = self.charts_dir / 'charts_summary.json'
        with open(summary_file, 'w') as f:
            json.dump(chart_summary, f, indent=2)

        return chart_summary

    def _extract_table_data(self, page):
        """Extract any HTML table data from the page"""
        print("\n🗂️  Looking for HTML tables...")

        try:
            tables_data = page.evaluate('''
                () => {
                    const tables = document.querySelectorAll('table');
                    return Array.from(tables).map((table, index) => {
                        const rows = Array.from(table.querySelectorAll('tr'));
                        return {
                            index: index,
                            headers: Array.from(rows[0]?.querySelectorAll('th, td') || []).map(cell => cell.textContent.trim()),
                            data: rows.slice(1).map(row =>
                                Array.from(row.querySelectorAll('td')).map(cell => cell.textContent.trim())
                            ).filter(row => row.length > 0)
                        };
                    }).filter(table => table.data.length > 0);
                }
            ''')

            if tables_data:
                print(f"📋 Found {len(tables_data)} tables")

                for table in tables_data:
                    table_file = self.raw_dir / f'table_{table["index"]}_data.csv'

                    with open(table_file, 'w', newline='', encoding='utf-8') as f:
                        writer = csv.writer(f)
                        if table['headers']:
                            writer.writerow(table['headers'])
                        writer.writerows(table['data'])

                    print(f"   💾 Table {table['index']}: {len(table['data'])} rows → {table_file.name}")
            else:
                print("   ℹ️  No HTML tables found")

        except Exception as e:
            print(f"❌ Error extracting table data: {e}")

    def _is_breakbulk_index(self, series_name, chart_title):
        """Check if a series is a breakbulk transport index"""
        breakbulk_keywords = ['BREAKBULK', 'PROJECT', 'GENERAL', 'CARGO', 'INDEX', 'TRANSPORT', 'MULTIPURPOSE']
        series_upper = series_name.upper()
        title_upper = chart_title.upper()

        return any(keyword in series_upper or keyword in title_upper for keyword in breakbulk_keywords)

    def _sanitize_filename(self, filename):
        """Sanitize filename for cross-platform compatibility"""
        # Remove or replace invalid characters
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        filename = re.sub(r'[（）\(\)]', '', filename)
        filename = re.sub(r'\s+', '_', filename)
        filename = filename.strip('_')
        return filename[:50]  # Limit length

    def _generate_summary(self):
        """Generate a comprehensive summary of extracted data"""
        print("\n📋 Generating summary...")

        summary = {
            'extraction_date': datetime.now().isoformat(),
            'source_url': self.base_url,
            'folders': {},
            'file_counts': {},
            'breakbulk_indices': []
        }

        # Count files in each directory
        for dir_name, dir_path in [
            ('drewry_charts_data', self.charts_dir),
            ('breakbulk_indices', self.indices_dir),
            ('drewry_raw_extracts', self.raw_dir)
        ]:
            if dir_path.exists():
                files = list(dir_path.rglob('*'))
                csv_files = [f for f in files if f.suffix == '.csv']
                json_files = [f for f in files if f.suffix == '.json']

                summary['folders'][dir_name] = str(dir_path)
                summary['file_counts'][dir_name] = {
                    'total_files': len(files),
                    'csv_files': len(csv_files),
                    'json_files': len(json_files)
                }

        # List breakbulk indices
        if self.indices_dir.exists():
            for csv_file in self.indices_dir.glob('*.csv'):
                summary['breakbulk_indices'].append(csv_file.name)

        # Save summary
        summary_file = self.output_dir / 'drewry_extraction_summary.json'
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2)

        print(f"📄 Summary saved: {summary_file}")

        # Print summary to console
        print(f"\n📊 DREWRY EXTRACTION SUMMARY")
        print(f"{'='*50}")
        print(f"📅 Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🌐 Source: {self.base_url}")
        print(f"\n📁 Files created:")
        for folder, counts in summary['file_counts'].items():
            print(f"   {folder}: {counts['csv_files']} CSV, {counts['json_files']} JSON")

        print(f"\n🚢 Breakbulk Indices Available:")
        for index in summary['breakbulk_indices']:
            print(f"   • {index}")

if __name__ == "__main__":
    extractor = DrewryBreakbulkDataExtractor()
    extractor.extract_all_data()
