from flask import Flask, request, jsonify
from datetime import datetime
import json
from pathlib import Path
from enhanced_shipping_calculator import EnhancedShippingCalculator
from port_database_manager import PortDatabaseManager
import pandas as pd

app = Flask(__name__)

# Initialize calculator
calculator = EnhancedShippingCalculator()
port_manager = PortDatabaseManager()

@app.route('/api/quote', methods=['POST'])
def get_shipping_quote():
    """
    Get shipping quote for specific route
    
    Expected JSON payload:
    {
        "origin_port": "Shanghai",
        "cargo_type": "General_Cargo",
        "dwt_used": 5000,
        "cargo_value_usd": 1000000
    }
    """
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['origin_port', 'cargo_type']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'Missing required field: {field}'}), 400
        
        # Set defaults
        dwt_used = data.get('dwt_used', 5000)
        cargo_value_usd = data.get('cargo_value_usd', 1000000)
        
        # Calculate quote
        quote = calculator.calculate_comprehensive_cost(
            origin_port=data['origin_port'],
            cargo_type=data['cargo_type'],
            dwt_used=dwt_used,
            cargo_value_usd=cargo_value_usd
        )
        
        return jsonify({
            'status': 'success',
            'quote': quote,
            'timestamp': datetime.now().isoformat()
        })
        
    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        return jsonify({'error': f'Internal server error: {str(e)}'}), 500

@app.route('/api/ports', methods=['GET'])
def get_available_ports():
    """Get list of all available ports"""
    try:
        ports_data = port_manager.ports_data
        
        # Filter and format port data
        ports_list = []
        for port_name, port_info in ports_data.items():
            ports_list.append({
                'port_name': port_name,
                'country': port_info['country'],
                'region': port_info['region'],
                'voyage_days_to_israel': port_info['voyage_days'],
                'handling_capabilities': port_info['handling_capabilities'],
                'major_commodities': port_info['major_commodities']
            })
        
        return jsonify({
            'status': 'success',
            'total_ports': len(ports_list),
            'ports': ports_list
        })
        
    except Exception as e:
        return jsonify({'error': f'Internal server error: {str(e)}'}), 500

@app.route('/api/ports/<region>', methods=['GET'])
def get_ports_by_region(region):
    """Get ports filtered by region"""
    try:
        ports_in_region = port_manager.get_ports_by_region(region)
        
        if not ports_in_region:
            return jsonify({'error': f'No ports found in region: {region}'}), 404
        
        ports_list = []
        for port_name, port_info in ports_in_region.items():
            ports_list.append({
                'port_name': port_name,
                'country': port_info['country'],
                'voyage_days_to_israel': port_info['voyage_days'],
                'handling_capabilities': port_info['handling_capabilities']
            })
        
        return jsonify({
            'status': 'success',
            'region': region,
            'total_ports': len(ports_list),
            'ports': ports_list
        })
        
    except Exception as e:
        return jsonify({'error': f'Internal server error: {str(e)}'}), 500

@app.route('/api/compare', methods=['POST'])
def compare_routes():
    """
    Compare shipping costs across multiple routes
    
    Expected JSON payload:
    {
        "ports": ["Shanghai", "Tianjin", "Mersin"],
        "cargo_type": "General_Cargo",
        "dwt_used": 5000
    }
    """
    try:
        data = request.get_json()
        
        if 'ports' not in data or not data['ports']:
            return jsonify({'error': 'Missing or empty ports list'}), 400
        
        cargo_type = data.get('cargo_type', 'General_Cargo')
        dwt_used = data.get('dwt_used', 5000)
        cargo_value_usd = data.get('cargo_value_usd', 1000000)
        
        comparisons = []
        for port in data['ports']:
            try:
                quote = calculator.calculate_comprehensive_cost(
                    origin_port=port,
                    cargo_type=cargo_type,
                    dwt_used=dwt_used,
                    cargo_value_usd=cargo_value_usd
                )
                comparisons.append(quote)
            except Exception as e:
                comparisons.append({
                    'origin_port': port,
                    'error': str(e)
                })
        
        # Sort by cost per ton
        valid_quotes = [q for q in comparisons if 'error' not in q]
        valid_quotes.sort(key=lambda x: x['cost_per_ton_usd'])
        
        return jsonify({
            'status': 'success',
            'comparison_results': comparisons,
            'best_option': valid_quotes[0] if valid_quotes else None,
            'total_compared': len(data['ports']),
            'successful_quotes': len(valid_quotes)
        })
        
    except Exception as e:
        return jsonify({'error': f'Internal server error: {str(e)}'}), 500

@app.route('/api/market-data', methods=['GET'])
def get_market_data():
    """Get current market indices and conditions"""
    try:
        # Get latest Baltic indices
        baltic_data = {}
        if not calculator.baltic_data.empty:
            for col in calculator.baltic_data.columns:
                index_name = col[0] if isinstance(col, tuple) else col
                latest_value = calculator.baltic_data[col].iloc[-1]
                baltic_data[index_name] = {
                    'value': float(latest_value),
                    'date': calculator.baltic_data.index[-1].isoformat()
                }
        
        # Get latest Drewry indices
        drewry_data = {}
        if not calculator.drewry_data.empty:
            for col in calculator.drewry_data.columns:
                index_name = col[0] if isinstance(col, tuple) else col
                latest_value = calculator.drewry_data[col].iloc[-1]
                drewry_data[index_name] = {
                    'value': float(latest_value),
                    'date': calculator.drewry_data.index[-1].isoformat()
                }
        
        return jsonify({
            'status': 'success',
            'market_data': {
                'baltic_indices': baltic_data,
                'drewry_indices': drewry_data,
                'market_conditions': calculator.market_conditions
            },
            'last_updated': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({'error': f'Internal server error: {str(e)}'}), 500

@app.route('/api/bulk-quotes', methods=['POST'])
def get_bulk_quotes():
    """
    Get quotes for all available ports and cargo types
    
    Optional JSON payload:
    {
        "cargo_types": ["General_Cargo", "Project_Cargo"],
        "dwt_used": 5000,
        "top_n": 10
    }
    """
    try:
        data = request.get_json() or {}
        
        cargo_types = data.get('cargo_types', ['General_Cargo', 'Project_Cargo'])
        dwt_used = data.get('dwt_used', 5000)
        top_n = data.get('top_n', 10)
        
        # Calculate for all available ports
        results_df = calculator.calculate_multiple_routes(
            ports=None,  # All ports
            cargo_types=cargo_types
        )
        
        if results_df.empty:
            return jsonify({'error': 'No quotes could be generated'}), 500
        
        # Convert to list of dictionaries
        all_quotes = results_df.to_dict('records')
        
        # Get top N most competitive routes
        top_quotes = results_df.nsmallest(top_n, 'Cost_Per_Ton_USD').to_dict('records')
        
        return jsonify({
            'status': 'success',
            'total_quotes': len(all_quotes),
            'top_competitive_routes': top_quotes,
            'all_quotes': all_quotes,
            'summary_stats': {
                'min_cost_per_ton': float(results_df['Cost_Per_Ton_USD'].min()),
                'max_cost_per_ton': float(results_df['Cost_Per_Ton_USD'].max()),
                'avg_cost_per_ton': float(results_df['Cost_Per_Ton_USD'].mean()),
                'countries_covered': int(results_df['Country'].nunique()),
                'ports_covered': int(results_df['Origin_Port'].nunique())
            }
        })
        
    except Exception as e:
        return jsonify({'error': f'Internal server error: {str(e)}'}), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'Shipping Quote API',
        'timestamp': datetime.now().isoformat(),
        'data_status': {
            'drewry_indices_available': not calculator.drewry_data.empty,
            'baltic_indices_available': not calculator.baltic_data.empty,
            'ports_in_database': len(port_manager.ports_data)
        }
    })

@app.route('/', methods=['GET'])
def api_info():
    """API information and documentation"""
    return jsonify({
        'service': 'Shipping Cost Calculator API',
        'version': '1.0',
        'description': 'Calculate breakbulk shipping costs to Israel using Baltic and Drewry indices',
        'endpoints': {
            'POST /api/quote': 'Get shipping quote for specific route',
            'GET /api/ports': 'Get list of all available ports',
            'GET /api/ports/<region>': 'Get ports filtered by region',
            'POST /api/compare': 'Compare shipping costs across multiple routes',
            'GET /api/market-data': 'Get current market indices and conditions',
            'POST /api/bulk-quotes': 'Get quotes for all available ports',
            'GET /api/health': 'Health check endpoint'
        },
        'supported_cargo_types': ['General_Cargo', 'Project_Cargo'],
        'supported_regions': list(set(port_manager.ports_data[port]['region'] 
                                    for port in port_manager.ports_data)),
        'total_ports': len(port_manager.ports_data)
    })

if __name__ == '__main__':
    print("Starting Shipping Quote API...")
    print("Available at: http://localhost:5000")
    print("API Documentation: http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)
