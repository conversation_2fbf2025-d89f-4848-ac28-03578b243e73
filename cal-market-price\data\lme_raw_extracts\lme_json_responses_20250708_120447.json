[{"url": "https://www.lme.com/api/trading-data/chart-data?datasourceId=d6171d98-db82-4d8b-94a4-6e3614974719&startDate=2025-06-06&endDate=2025-07-07", "status": 200, "content_type": "application/json; charset=utf-8", "data": {"HistoricalDataLookbackEnabled": true, "HistoricalDataLookbackRange": 5, "HistoricalDataLookbackUnit": 1, "DateOfData": "2025-07-07T00:00:00", "LookbackDate": "2020-07-08T00:00:00+00:00", "Labels": ["06/06/2025", "09/06/2025", "10/06/2025", "11/06/2025", "12/06/2025", "13/06/2025", "16/06/2025", "17/06/2025", "18/06/2025", "19/06/2025", "20/06/2025", "23/06/2025", "24/06/2025", "25/06/2025", "26/06/2025", "27/06/2025", "30/06/2025", "01/07/2025", "02/07/2025", "03/07/2025", "04/07/2025", "07/07/2025"], "Datasets": [{"RowTitle": "Month 2", "RowId": "b97847db-8d31-4ac1-a0e0-0ec07b86a8d2", "Label": "Price", "Data": ["547.50", "546.00", "545.00", "540.50", "541.50", "541.50", "540.50", "538.00", "540.50", "540.50", "537.50", "540.50", "540.50", "540.50", "536.50", "540.50", "536.00", "540.50", "539.50", "539.50", "539.50", "537.50"], "Hover": ["Expiry date 31 JUL 2025", "Expiry date 31 JUL 2025", "Expiry date 31 JUL 2025", "Expiry date 31 JUL 2025", "Expiry date 31 JUL 2025", "Expiry date 31 JUL 2025", "Expiry date 31 JUL 2025", "Expiry date 31 JUL 2025", "Expiry date 31 JUL 2025", "Expiry date 31 JUL 2025", "Expiry date 31 JUL 2025", "Expiry date 31 JUL 2025", "Expiry date 31 JUL 2025", "Expiry date 31 JUL 2025", "Expiry date 31 JUL 2025", "Expiry date 31 JUL 2025", "Expiry date 31 JUL 2025", "Expiry date 29 AUG 2025", "Expiry date 29 AUG 2025", "Expiry date 29 AUG 2025", "Expiry date 29 AUG 2025", "Expiry date 29 AUG 2025"]}, {"RowTitle": "Month 1", "RowId": "d4848701-c61b-4aaf-974f-eff1f3762ebd", "Label": "Price", "Data": ["546.00", "546.00", "549.50", "541.00", "541.50", "541.50", "540.50", "540.50", "540.50", "540.50", "540.50", "540.50", "540.50", "540.50", "541.50", "541.50", "542.95", "537.50", "539.50", "539.50", "539.50", "536.00"], "Hover": ["Expiry date 30 JUN 2025", "Expiry date 30 JUN 2025", "Expiry date 30 JUN 2025", "Expiry date 30 JUN 2025", "Expiry date 30 JUN 2025", "Expiry date 30 JUN 2025", "Expiry date 30 JUN 2025", "Expiry date 30 JUN 2025", "Expiry date 30 JUN 2025", "Expiry date 30 JUN 2025", "Expiry date 30 JUN 2025", "Expiry date 30 JUN 2025", "Expiry date 30 JUN 2025", "Expiry date 30 JUN 2025", "Expiry date 30 JUN 2025", "Expiry date 30 JUN 2025", "Expiry date 30 JUN 2025", "Expiry date 31 JUL 2025", "Expiry date 31 JUL 2025", "Expiry date 31 JUL 2025", "Expiry date 31 JUL 2025", "Expiry date 31 JUL 2025"]}, {"RowTitle": "Month 3", "RowId": "d1df0a2f-b53b-49ad-b034-384534d280b7", "Label": "Price", "Data": ["546.00", "544.50", "546.50", "546.50", "548.00", "546.50", "543.50", "543.50", "547.50", "542.00", "541.00", "540.50", "541.00", "539.50", "536.00", "539.50", "536.00", "542.50", "547.00", "542.50", "542.50", "542.50"], "Hover": ["Expiry date 29 AUG 2025", "Expiry date 29 AUG 2025", "Expiry date 29 AUG 2025", "Expiry date 29 AUG 2025", "Expiry date 29 AUG 2025", "Expiry date 29 AUG 2025", "Expiry date 29 AUG 2025", "Expiry date 29 AUG 2025", "Expiry date 29 AUG 2025", "Expiry date 29 AUG 2025", "Expiry date 29 AUG 2025", "Expiry date 29 AUG 2025", "Expiry date 29 AUG 2025", "Expiry date 29 AUG 2025", "Expiry date 29 AUG 2025", "Expiry date 29 AUG 2025", "Expiry date 29 AUG 2025", "Expiry date 30 SEP 2025", "Expiry date 30 SEP 2025", "Expiry date 30 SEP 2025", "Expiry date 30 SEP 2025", "Expiry date 30 SEP 2025"]}, {"RowTitle": "Month 4", "RowId": "e3f81247-de18-4e47-97d4-7486f291ae4a", "Label": "Price", "Data": ["550.50", "547.50", "546.50", "549.50", "550.00", "549.50", "548.50", "547.50", "547.50", "542.00", "546.50", "541.50", "538.50", "541.50", "537.50", "541.50", "536.00", "545.00", "547.50", "545.50", "545.50", "546.50"], "Hover": ["Expiry date 30 SEP 2025", "Expiry date 30 SEP 2025", "Expiry date 30 SEP 2025", "Expiry date 30 SEP 2025", "Expiry date 30 SEP 2025", "Expiry date 30 SEP 2025", "Expiry date 30 SEP 2025", "Expiry date 30 SEP 2025", "Expiry date 30 SEP 2025", "Expiry date 30 SEP 2025", "Expiry date 30 SEP 2025", "Expiry date 30 SEP 2025", "Expiry date 30 SEP 2025", "Expiry date 30 SEP 2025", "Expiry date 30 SEP 2025", "Expiry date 30 SEP 2025", "Expiry date 30 SEP 2025", "Expiry date 31 OCT 2025", "Expiry date 31 OCT 2025", "Expiry date 31 OCT 2025", "Expiry date 31 OCT 2025", "Expiry date 31 OCT 2025"]}, {"RowTitle": "Month 5", "RowId": "8412dcaf-0203-4533-bf9c-88083aedcbbe", "Label": "Price", "Data": ["556.00", "555.00", "555.00", "555.00", "552.50", "556.50", "557.50", "554.00", "551.00", "547.00", "549.50", "546.50", "542.00", "542.00", "542.50", "543.50", "537.50", "545.00", "552.00", "545.00", "545.00", "547.50"], "Hover": ["Expiry date 31 OCT 2025", "Expiry date 31 OCT 2025", "Expiry date 31 OCT 2025", "Expiry date 31 OCT 2025", "Expiry date 31 OCT 2025", "Expiry date 31 OCT 2025", "Expiry date 31 OCT 2025", "Expiry date 31 OCT 2025", "Expiry date 31 OCT 2025", "Expiry date 31 OCT 2025", "Expiry date 31 OCT 2025", "Expiry date 31 OCT 2025", "Expiry date 31 OCT 2025", "Expiry date 31 OCT 2025", "Expiry date 31 OCT 2025", "Expiry date 31 OCT 2025", "Expiry date 31 OCT 2025", "Expiry date 28 NOV 2025", "Expiry date 28 NOV 2025", "Expiry date 28 NOV 2025", "Expiry date 28 NOV 2025", "Expiry date 28 NOV 2025"]}, {"RowTitle": "Month 6", "RowId": "2443dea7-c558-4794-9b27-e0f4c9e715af", "Label": "Price", "Data": ["560.00", "562.50", "560.00", "564.50", "557.00", "564.50", "560.00", "556.00", "555.00", "555.00", "555.50", "548.00", "543.50", "543.00", "544.00", "546.50", "542.00", "545.00", "550.50", "550.50", "550.50", "550.50"], "Hover": ["Expiry date 28 NOV 2025", "Expiry date 28 NOV 2025", "Expiry date 28 NOV 2025", "Expiry date 28 NOV 2025", "Expiry date 28 NOV 2025", "Expiry date 28 NOV 2025", "Expiry date 28 NOV 2025", "Expiry date 28 NOV 2025", "Expiry date 28 NOV 2025", "Expiry date 28 NOV 2025", "Expiry date 28 NOV 2025", "Expiry date 28 NOV 2025", "Expiry date 28 NOV 2025", "Expiry date 28 NOV 2025", "Expiry date 28 NOV 2025", "Expiry date 28 NOV 2025", "Expiry date 28 NOV 2025", "Expiry date 31 DEC 2025", "Expiry date 31 DEC 2025", "Expiry date 31 DEC 2025", "Expiry date 31 DEC 2025", "Expiry date 31 DEC 2025"]}, {"RowTitle": "Month 7", "RowId": "6cb5c087-4fd7-4b7c-bccd-f2165788609a", "Label": "Price", "Data": ["568.50", "568.50", "568.50", "568.50", "568.50", "568.50", "564.50", "564.50", "564.50", "562.50", "562.50", "562.50", "552.00", "547.00", "552.50", "552.50", "550.50", "556.50", "555.50", "555.50", "555.50", "555.50"], "Hover": ["Expiry date 31 DEC 2025", "Expiry date 31 DEC 2025", "Expiry date 31 DEC 2025", "Expiry date 31 DEC 2025", "Expiry date 31 DEC 2025", "Expiry date 31 DEC 2025", "Expiry date 31 DEC 2025", "Expiry date 31 DEC 2025", "Expiry date 31 DEC 2025", "Expiry date 31 DEC 2025", "Expiry date 31 DEC 2025", "Expiry date 31 DEC 2025", "Expiry date 31 DEC 2025", "Expiry date 31 DEC 2025", "Expiry date 31 DEC 2025", "Expiry date 31 DEC 2025", "Expiry date 31 DEC 2025", "Expiry date 30 JAN 2026", "Expiry date 30 JAN 2026", "Expiry date 30 JAN 2026", "Expiry date 30 JAN 2026", "Expiry date 30 JAN 2026"]}, {"RowTitle": "Month 8", "RowId": "8e22347d-94eb-47fa-bae7-ba71472dc68b", "Label": "Price", "Data": ["572.50", "572.50", "572.50", "572.50", "572.50", "572.50", "569.50", "569.50", "569.50", "566.50", "566.50", "566.50", "562.00", "557.50", "555.00", "557.50", "555.50", "562.00", "561.50", "561.50", "561.50", "561.50"], "Hover": ["Expiry date 30 JAN 2026", "Expiry date 30 JAN 2026", "Expiry date 30 JAN 2026", "Expiry date 30 JAN 2026", "Expiry date 30 JAN 2026", "Expiry date 30 JAN 2026", "Expiry date 30 JAN 2026", "Expiry date 30 JAN 2026", "Expiry date 30 JAN 2026", "Expiry date 30 JAN 2026", "Expiry date 30 JAN 2026", "Expiry date 30 JAN 2026", "Expiry date 30 JAN 2026", "Expiry date 30 JAN 2026", "Expiry date 30 JAN 2026", "Expiry date 30 JAN 2026", "Expiry date 30 JAN 2026", "Expiry date 27 FEB 2026", "Expiry date 27 FEB 2026", "Expiry date 27 FEB 2026", "Expiry date 27 FEB 2026", "Expiry date 27 FEB 2026"]}, {"RowTitle": "Month 9", "RowId": "5e654f2f-6fd0-41c2-965d-d7bbb23867df", "Label": "Price", "Data": ["576.50", "576.50", "576.50", "576.50", "576.50", "576.50", "574.50", "574.50", "574.50", "571.50", "571.50", "571.50", "571.50", "563.50", "563.50", "563.50", "561.50", "572.50", "572.50", "572.50", "572.50", "572.50"], "Hover": ["Expiry date 27 FEB 2026", "Expiry date 27 FEB 2026", "Expiry date 27 FEB 2026", "Expiry date 27 FEB 2026", "Expiry date 27 FEB 2026", "Expiry date 27 FEB 2026", "Expiry date 27 FEB 2026", "Expiry date 27 FEB 2026", "Expiry date 27 FEB 2026", "Expiry date 27 FEB 2026", "Expiry date 27 FEB 2026", "Expiry date 27 FEB 2026", "Expiry date 27 FEB 2026", "Expiry date 27 FEB 2026", "Expiry date 27 FEB 2026", "Expiry date 27 FEB 2026", "Expiry date 27 FEB 2026", "Expiry date 31 MAR 2026", "Expiry date 31 MAR 2026", "Expiry date 31 MAR 2026", "Expiry date 31 MAR 2026", "Expiry date 31 MAR 2026"]}, {"RowTitle": "Month 10", "RowId": "292b3d51-4309-448a-bfca-c2e54a850d19", "Label": "Price", "Data": ["581.50", "581.50", "581.50", "581.50", "581.50", "581.50", "582.00", "582.00", "582.00", "579.00", "579.00", "579.00", "579.00", "572.50", "572.50", "572.50", "572.50", "574.00", "581.50", "581.50", "581.50", "581.50"], "Hover": ["Expiry date 31 MAR 2026", "Expiry date 31 MAR 2026", "Expiry date 31 MAR 2026", "Expiry date 31 MAR 2026", "Expiry date 31 MAR 2026", "Expiry date 31 MAR 2026", "Expiry date 31 MAR 2026", "Expiry date 31 MAR 2026", "Expiry date 31 MAR 2026", "Expiry date 31 MAR 2026", "Expiry date 31 MAR 2026", "Expiry date 31 MAR 2026", "Expiry date 31 MAR 2026", "Expiry date 31 MAR 2026", "Expiry date 31 MAR 2026", "Expiry date 31 MAR 2026", "Expiry date 31 MAR 2026", "Expiry date 30 APR 2026", "Expiry date 30 APR 2026", "Expiry date 30 APR 2026", "Expiry date 30 APR 2026", "Expiry date 30 APR 2026"]}, {"RowTitle": "Month 11", "RowId": "d5fd9364-47d9-4638-a690-a5a6092fbc66", "Label": "Price", "Data": ["586.50", "586.50", "586.50", "586.50", "586.50", "586.50", "589.50", "589.50", "589.50", "586.50", "586.50", "586.50", "586.50", "581.50", "574.00", "581.50", "581.50", "582.50", "582.50", "582.50", "582.50", "582.50"], "Hover": ["Expiry date 30 APR 2026", "Expiry date 30 APR 2026", "Expiry date 30 APR 2026", "Expiry date 30 APR 2026", "Expiry date 30 APR 2026", "Expiry date 30 APR 2026", "Expiry date 30 APR 2026", "Expiry date 30 APR 2026", "Expiry date 30 APR 2026", "Expiry date 30 APR 2026", "Expiry date 30 APR 2026", "Expiry date 30 APR 2026", "Expiry date 30 APR 2026", "Expiry date 30 APR 2026", "Expiry date 30 APR 2026", "Expiry date 30 APR 2026", "Expiry date 30 APR 2026", "Expiry date 29 MAY 2026", "Expiry date 29 MAY 2026", "Expiry date 29 MAY 2026", "Expiry date 29 MAY 2026", "Expiry date 29 MAY 2026"]}, {"RowTitle": "Month 12", "RowId": "4989f786-273d-4e9e-974c-d773d2a7f500", "Label": "Price", "Data": ["589.50", "589.50", "589.50", "589.50", "589.50", "589.50", "594.50", "594.50", "594.50", "591.50", "591.50", "591.50", "591.50", "586.50", "579.00", "582.50", "582.50", "584.50", "584.50", "584.50", "584.50", "584.50"], "Hover": ["Expiry date 29 MAY 2026", "Expiry date 29 MAY 2026", "Expiry date 29 MAY 2026", "Expiry date 29 MAY 2026", "Expiry date 29 MAY 2026", "Expiry date 29 MAY 2026", "Expiry date 29 MAY 2026", "Expiry date 29 MAY 2026", "Expiry date 29 MAY 2026", "Expiry date 29 MAY 2026", "Expiry date 29 MAY 2026", "Expiry date 29 MAY 2026", "Expiry date 29 MAY 2026", "Expiry date 29 MAY 2026", "Expiry date 29 MAY 2026", "Expiry date 29 MAY 2026", "Expiry date 29 MAY 2026", "Expiry date 30 JUN 2026", "Expiry date 30 JUN 2026", "Expiry date 30 JUN 2026", "Expiry date 30 JUN 2026", "Expiry date 30 JUN 2026"]}, {"RowTitle": "Month 13", "RowId": "c9bae745-52e0-46b7-a1cf-3b39b9650192", "Label": "Price", "Data": ["596.50", "596.50", "596.50", "596.50", "596.50", "596.50", "594.50", "594.50", "594.50", "594.50", "594.50", "594.50", "594.50", "592.50", "585.00", "584.50", "584.50", "584.50", "584.50", "584.50", "584.50", "584.50"], "Hover": ["Expiry date 30 JUN 2026", "Expiry date 30 JUN 2026", "Expiry date 30 JUN 2026", "Expiry date 30 JUN 2026", "Expiry date 30 JUN 2026", "Expiry date 30 JUN 2026", "Expiry date 30 JUN 2026", "Expiry date 30 JUN 2026", "Expiry date 30 JUN 2026", "Expiry date 30 JUN 2026", "Expiry date 30 JUN 2026", "Expiry date 30 JUN 2026", "Expiry date 30 JUN 2026", "Expiry date 30 JUN 2026", "Expiry date 30 JUN 2026", "Expiry date 30 JUN 2026", "Expiry date 30 JUN 2026", "Expiry date 31 JUL 2026", "Expiry date 31 JUL 2026", "Expiry date 31 JUL 2026", "Expiry date 31 JUL 2026", "Expiry date 31 JUL 2026"]}, {"RowTitle": "Month 14", "RowId": "0a359786-cb0a-41f4-8443-75d2d0e4587a", "Label": "Price", "Data": ["596.50", "596.50", "596.50", "596.50", "596.50", "596.50", "594.50", "594.50", "594.50", "594.50", "594.50", "594.50", "594.50", "592.50", "585.00", "584.50", "584.50", "584.50", "584.50", "584.50", "584.50", "584.50"], "Hover": ["Expiry date 31 JUL 2026", "Expiry date 31 JUL 2026", "Expiry date 31 JUL 2026", "Expiry date 31 JUL 2026", "Expiry date 31 JUL 2026", "Expiry date 31 JUL 2026", "Expiry date 31 JUL 2026", "Expiry date 31 JUL 2026", "Expiry date 31 JUL 2026", "Expiry date 31 JUL 2026", "Expiry date 31 JUL 2026", "Expiry date 31 JUL 2026", "Expiry date 31 JUL 2026", "Expiry date 31 JUL 2026", "Expiry date 31 JUL 2026", "Expiry date 31 JUL 2026", "Expiry date 31 JUL 2026", "Expiry date 28 AUG 2026", "Expiry date 28 AUG 2026", "Expiry date 28 AUG 2026", "Expiry date 28 AUG 2026", "Expiry date 28 AUG 2026"]}, {"RowTitle": "Month 15", "RowId": "957438c5-d774-49a6-929c-12d650a72e74", "Label": "Price", "Data": ["596.50", "596.50", "596.50", "596.50", "596.50", "596.50", "594.50", "594.50", "594.50", "594.50", "594.50", "594.50", "594.50", "592.50", "585.00", "584.50", "584.50", "584.50", "584.50", "584.50", "584.50", "584.50"], "Hover": ["Expiry date 28 AUG 2026", "Expiry date 28 AUG 2026", "Expiry date 28 AUG 2026", "Expiry date 28 AUG 2026", "Expiry date 28 AUG 2026", "Expiry date 28 AUG 2026", "Expiry date 28 AUG 2026", "Expiry date 28 AUG 2026", "Expiry date 28 AUG 2026", "Expiry date 28 AUG 2026", "Expiry date 28 AUG 2026", "Expiry date 28 AUG 2026", "Expiry date 28 AUG 2026", "Expiry date 28 AUG 2026", "Expiry date 28 AUG 2026", "Expiry date 28 AUG 2026", "Expiry date 28 AUG 2026", "Expiry date 30 SEP 2026", "Expiry date 30 SEP 2026", "Expiry date 30 SEP 2026", "Expiry date 30 SEP 2026", "Expiry date 30 SEP 2026"]}]}, "timestamp": "2025-07-08T12:03:28.592598"}, {"url": "https://www.lme.com/api/trading-data/nonworking-days", "status": 200, "content_type": "application/json; charset=utf-8", "data": ["2025-12-26T00:00:00", "2025-12-25T00:00:00", "2025-05-05T00:00:00", "2025-04-21T00:00:00", "2025-04-18T00:00:00", "2025-01-01T00:00:00", "2025-05-26T00:00:00", "2025-08-25T00:00:00", "2024-12-26T00:00:00", "2024-12-25T00:00:00", "2024-05-06T00:00:00", "2024-04-01T00:00:00", "2024-03-29T00:00:00", "2024-01-01T00:00:00", "2024-05-27T00:00:00", "2024-08-26T00:00:00", "2023-01-02T00:00:00", "2023-04-07T00:00:00", "2023-12-26T00:00:00", "2023-12-25T00:00:00", "2023-05-08T00:00:00", "2023-05-01T00:00:00", "2023-04-10T00:00:00", "2023-05-29T00:00:00", "2023-08-28T00:00:00", "2022-01-03T00:00:00", "2022-04-15T00:00:00", "2022-04-18T00:00:00", "2022-05-02T00:00:00", "2022-06-02T00:00:00", "2022-06-03T00:00:00", "2022-08-29T00:00:00", "2022-12-27T00:00:00", "2022-12-26T00:00:00", "2021-01-01T00:00:00", "2021-04-02T00:00:00", "2021-04-05T00:00:00", "2021-05-03T00:00:00", "2021-05-31T00:00:00", "2021-08-30T00:00:00", "2021-12-27T00:00:00", "2021-12-28T00:00:00", "2020-01-01T00:00:00", "2020-04-10T00:00:00", "2020-04-13T00:00:00", "2020-05-04T00:00:00", "2020-05-08T00:00:00", "2020-05-25T00:00:00", "2020-08-31T00:00:00", "2020-12-25T00:00:00", "2020-12-28T00:00:00", "2019-01-01T00:00:00", "2019-04-19T00:00:00", "2019-04-22T00:00:00", "2019-05-06T00:00:00", "2019-05-27T00:00:00", "2019-08-26T00:00:00", "2019-12-25T00:00:00", "2019-12-26T00:00:00", "2018-01-01T00:00:00", "2018-03-30T00:00:00", "2018-04-02T00:00:00", "2018-05-07T00:00:00", "2018-05-28T00:00:00", "2018-08-27T00:00:00", "2018-12-25T00:00:00", "2018-12-26T00:00:00", "2017-01-02T00:00:00", "2017-04-14T00:00:00", "2017-04-17T00:00:00", "2017-05-01T00:00:00", "2017-05-29T00:00:00", "2017-08-28T00:00:00", "2017-12-25T00:00:00", "2017-12-26T00:00:00", "2016-01-01T00:00:00", "2016-03-25T00:00:00", "2016-03-28T00:00:00", "2016-05-02T00:00:00", "2016-05-30T00:00:00", "2016-08-29T00:00:00", "2016-12-27T00:00:00", "2016-12-26T00:00:00"], "timestamp": "2025-07-08T12:03:28.619048"}]