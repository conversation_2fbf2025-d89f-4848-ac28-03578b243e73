# Enhanced Shipping Cost Calculator for Israel Routes

A comprehensive system for calculating breakbulk shipping costs to Israel from global ports using Baltic indices and Drewry breakbulk data.

## 🚀 Features

- **Multi-Index Integration**: Uses Baltic indices (BSI, BDI, BCI, BPI, BHSI, VLCC) and Drewry breakbulk indices
- **Comprehensive Port Database**: 14+ ports across China, Turkey, Russia, and Portugal with expansion capability
- **Detailed Cost Breakdown**: Vessel charter, fuel, crew, port handling, canal fees, insurance, risk premiums
- **Multiple Interfaces**: Command-line tool, Python API, and REST API
- **Real-time Market Data**: Current market indices and conditions
- **Advanced Analytics**: Route comparison, competitive analysis, and export capabilities

## 📁 File Structure

```
cal-market-price/
├── test-v5.py                      # Original enhanced calculator
├── port_database_manager.py        # Port database management
├── enhanced_shipping_calculator.py # Advanced cost calculation engine
├── shipping_quote_api.py          # REST API interface
├── shipping_cli.py                # Command-line interface
└── README.md                       # This file
```

## 🛠️ Installation

1. **Prerequisites**:
   ```bash
   pip install pandas numpy matplotlib seaborn flask pathlib
   ```

2. **Data Requirements**:
   - Drewry breakbulk indices in `data/breakbulk_indices/`
   - Baltic indices in `data/shipping_indices/`
   - Port database (auto-generated)

## 💻 Usage

### Command Line Interface

```bash
# Get shipping quote for specific port
python shipping_cli.py quote --port Shanghai --cargo General_Cargo --detailed

# Compare multiple ports
python shipping_cli.py compare --ports Shanghai Tianjin Mersin --cargo Project_Cargo

# List all available ports
python shipping_cli.py ports

# Show top 5 most competitive routes
python shipping_cli.py top --limit 5

# Export all calculations to CSV
python shipping_cli.py export --output my_shipping_costs.csv

# Show current market data
python shipping_cli.py market
```

### Python API

```python
from enhanced_shipping_calculator import EnhancedShippingCalculator

# Initialize calculator
calculator = EnhancedShippingCalculator()

# Get comprehensive quote
quote = calculator.calculate_comprehensive_cost(
    origin_port="Shanghai",
    cargo_type="General_Cargo",
    dwt_used=5000,
    cargo_value_usd=1000000
)

print(f"Cost per ton: ${quote['cost_per_ton_usd']:.2f}")
print(f"Total cost: ${quote['total_cost_usd']:,.2f}")

# Calculate for multiple routes
results_df = calculator.calculate_multiple_routes()
```

### REST API

Start the API server:
```bash
python shipping_quote_api.py
```

API endpoints:
- `POST /api/quote` - Get shipping quote
- `GET /api/ports` - List available ports
- `POST /api/compare` - Compare multiple routes
- `GET /api/market-data` - Current market indices
- `POST /api/bulk-quotes` - All route combinations

Example API usage:
```bash
# Get quote
curl -X POST http://localhost:5000/api/quote \
  -H "Content-Type: application/json" \
  -d '{"origin_port": "Shanghai", "cargo_type": "General_Cargo"}'

# List ports
curl http://localhost:5000/api/ports
```

## 📊 Supported Ports

### Current Port Database (14 ports):

**China** (6 ports):
- Tianjin, Shanghai, Rizhao, Qingdao, Dalian, Ningbo

**Turkey** (5 ports):
- Mersin, Iskenderun, Nemrut Bay, Izmir, Aliaga

**Russia** (1 port):
- Novorossiysk

**Portugal** (2 ports):
- Sines, Leixões

### Port Capabilities:
- **Breakbulk**: All ports
- **Container**: Most major commercial ports
- **Bulk**: Commodity-specialized ports
- **Tanker**: Oil terminals and major ports

## 📈 Market Data Sources

### Baltic Indices:
- **BSI** (Baltic Supramax Index) - Primary for general cargo
- **BDI** (Baltic Dry Index) - Overall dry bulk market
- **BCI** (Baltic Capesize Index) - Large vessel rates
- **BPI** (Baltic Panamax Index) - Medium vessel rates
- **BHSI** (Baltic Handysize Index) - Small vessel rates
- **VLCC** (Very Large Crude Carrier) - Tanker rates

### Drewry Indices:
- **General Cargo Index** - Multi-purpose vessels (3,000-20,000 DWT)
- **Project Cargo Index** - Specialized project vessels (10,000-25,000 DWT)

## 💰 Cost Components

The system calculates comprehensive shipping costs including:

1. **Vessel Charter** - Daily charter rates based on market indices
2. **Fuel Costs** - Bunker consumption at current market prices
3. **Crew Costs** - Daily crew expenses
4. **Port Handling** - Loading/unloading fees
5. **Canal Fees** - Suez Canal transit costs (where applicable)
6. **Insurance** - Marine cargo insurance
7. **Risk Premiums** - Regional risk adjustments
8. **Red Sea Surcharge** - Additional costs for high-risk routes

## 🌍 Regional Factors

The system applies regional adjustments based on:
- **Distance and voyage time**
- **Geopolitical risk levels**
- **Port efficiency and congestion**
- **Regional market conditions**

### Risk Premiums by Region:
- China: 8% (Red Sea risk)
- Russia: 12% (Geopolitical risk)
- Turkey: 2% (Regional stability)
- Mediterranean: 3% (Standard risk)
- Northern Europe: 1% (Low risk)

## 📋 Output Examples

### Single Quote:
```
Origin Port: Shanghai (China)
Cargo Type: General_Cargo
Voyage Days: 51.5
💰 COST PER TON: $468.55
💰 TOTAL COST: $2,342,743

📊 DETAILED BREAKDOWN:
  Vessel Charter: $180,559
  Fuel: $836,875
  Crew: $61,800
  Port Handling: $125,000
  Canal Fees: $450,000
  Insurance: $2,000
  Risk Premium: $132,499
  Red Sea Surcharge: $248,435
```

### Route Comparison:
```
🏆 COMPARISON RESULTS:
Rank Port            Country      Days   $/ton      Total Cost  
1    Mersin          Turkey       5.0    $44.92     $224,595
2    Novorossiysk    Russia       9.0    $180.07    $900,350
3    Shanghai        China        51.5   $468.55    $2,342,743
```

## 🔧 Configuration

### Market Conditions (Configurable):
```python
market_conditions = {
    'bunker_price_usd_mt': 650,      # Current bunker fuel price
    'suez_canal_fee': 450000,        # USD for typical vessel
    'insurance_rate': 0.002,         # % of cargo value
    'red_sea_surcharge': 0.15        # 15% surcharge
}
```

### Vessel Profiles:
- **General Cargo**: 3,000-20,000 DWT, 70% Drewry weight
- **Project Cargo**: 10,000-25,000 DWT, 90% Drewry weight

## 📈 Future Enhancements

1. **Real-time Data Integration**: Automatic index updates
2. **More Ports**: European, African, and American ports
3. **Container Rates**: Integration with container shipping
4. **Weather Impact**: Seasonal and weather-based adjustments
5. **API Authentication**: Secure API access
6. **Historical Analysis**: Trend analysis and forecasting

## 🤝 Contributing

To add new ports or enhance functionality:

1. Use `PortDatabaseManager` to add ports
2. Update regional factors as needed
3. Test with existing data sources
4. Update documentation

## 📞 Support

For questions or issues:
- Check the CLI help: `python shipping_cli.py --help`
- Review API documentation: `http://localhost:5000`
- Examine example outputs in `reports/` directory

---

**Last Updated**: July 2025  
**Version**: 1.0  
**Data Sources**: Baltic Exchange, Drewry Maritime Research
