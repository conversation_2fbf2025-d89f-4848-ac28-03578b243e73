import pandas as pd
import numpy as np
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import json
from port_database_manager import PortDatabaseManager

class EnhancedShippingCalculator:
    """
    Enhanced shipping cost calculator with comprehensive port database integration
    Calculates breakbulk shipping costs to Israel using Baltic and Drewry indices
    """
    
    def __init__(self):
        self.data_path = Path(r"C:\Users\<USER>\Code\shipment\data")
        self.breakbulk_path = self.data_path / "breakbulk_indices"
        self.port_manager = PortDatabaseManager()
        
        # Load market data
        self.drewry_data = self._load_drewry_indices()
        self.baltic_data = self._load_baltic_indices()
        
        # Get port data from port manager
        self.voyage_days_map, self.port_to_country = self.port_manager.export_for_cost_engine()
        
        # Enhanced vessel profiles
        self.vessel_profiles = {
            'General_Cargo': {
                'dwt_range': (3000, 20000),
                'base_rate': 8500,
                'index_weight': 0.7,  # Higher weight to Drewry
                'handling_cost': 25,
                'fuel_consumption': 25,  # MT/day
                'crew_cost': 1200  # USD/day
            },
            'Project_Cargo': {
                'dwt_range': (10000, 25000),
                'base_rate': 12000,
                'index_weight': 0.9,  # Even higher Drewry weight
                'handling_cost': 40,
                'fuel_consumption': 35,  # MT/day
                'crew_cost': 1800  # USD/day
            }
        }
        
        # Enhanced region factors
        self.region_factors = {
            'China': {'General': 1.15, 'Project': 1.25, 'risk_premium': 0.08},
            'Russia': {'General': 1.05, 'Project': 1.15, 'risk_premium': 0.12},
            'Turkey': {'General': 0.95, 'Project': 1.05, 'risk_premium': 0.02},
            'Mediterranean': {'General': 0.95, 'Project': 1.05, 'risk_premium': 0.03},
            'Northern_Europe': {'General': 1.08, 'Project': 1.18, 'risk_premium': 0.01},
            'Eastern_Europe': {'General': 1.02, 'Project': 1.12, 'risk_premium': 0.05},
            'Portugal': {'General': 1.0, 'Project': 1.1, 'risk_premium': 0.02},
            'default': {'General': 1.0, 'Project': 1.1, 'risk_premium': 0.05}
        }
        
        # Current market conditions (can be updated dynamically)
        self.market_conditions = {
            'bunker_price_usd_mt': 650,  # Current bunker fuel price
            'suez_canal_fee': 450000,    # USD for typical vessel
            'insurance_rate': 0.002,     # % of cargo value
            'red_sea_surcharge': 0.15    # 15% surcharge for Red Sea routes
        }
    
    def _load_drewry_indices(self):
        """Load Drewry breakbulk indices"""
        print("[INFO] Loading Drewry indices...")
        indices = {}
        for file in self.breakbulk_path.glob('*_Index_data.csv'):
            name = file.stem.replace('_data', '')
            df = pd.read_csv(file, parse_dates=['date'], date_format='%b-%Y')
            df['value'] = pd.to_numeric(df['value'], errors='coerce')
            df = df.dropna().set_index('date')
            indices[name] = df
        
        combined = pd.concat(indices.values(), axis=1, keys=indices.keys())
        return combined.ffill()
    
    def _load_baltic_indices(self):
        """Load all Baltic indices"""
        print("[INFO] Loading Baltic indices...")
        indices_path = self.data_path / "shipping_indices"
        baltic_files = {
            'BSI': 'BSI_data.csv', 'BDI': 'BDI_data.csv', 'BCI': 'BCI_data.csv',
            'BPI': 'BPI_data.csv', 'BHSI': 'BHSI_data.csv', 'VLCC': 'VLCC_data.csv'
        }
        
        indices = {}
        for index_name, filename in baltic_files.items():
            file_path = indices_path / filename
            if file_path.exists():
                df = pd.read_csv(file_path, parse_dates=['date'], date_format='%Y/%m/%d')
                df['value'] = pd.to_numeric(df['value'], errors='coerce')
                df = df.dropna().drop_duplicates(subset=['date'], keep='last').set_index('date')
                if not df.empty:
                    indices[index_name] = df
        
        if indices:
            combined = pd.concat(indices.values(), axis=1, keys=indices.keys())
            return combined.ffill()
        return pd.DataFrame()
    
    def calculate_comprehensive_cost(self, origin_port: str, cargo_type: str = 'General_Cargo', 
                                   dwt_used: int = 5000, cargo_value_usd: float = 1000000) -> dict:
        """
        Calculate comprehensive shipping cost with detailed breakdown
        """
        if origin_port not in self.voyage_days_map:
            raise ValueError(f"Port {origin_port} not found in database")
        
        voyage_days = self.voyage_days_map[origin_port]
        country = self.port_to_country[origin_port]
        region = self._map_region(origin_port)
        
        # Get market rates
        drewry_key = f'{cargo_type}_Index'
        drewry_rate = self.drewry_data[drewry_key].iloc[-1]['value']
        baltic_rate = self._get_optimal_baltic_rate(cargo_type, dwt_used)
        
        profile = self.vessel_profiles[cargo_type]
        
        # Base daily rate calculation
        blended_daily_rate = (baltic_rate * (1 - profile['index_weight']) + 
                             drewry_rate * profile['index_weight'])
        
        # Detailed cost breakdown
        costs = {}
        
        # 1. Vessel charter cost
        costs['vessel_charter'] = blended_daily_rate * voyage_days
        
        # 2. Fuel costs
        bunker_cost = (self.market_conditions['bunker_price_usd_mt'] * 
                      profile['fuel_consumption'] * voyage_days)
        costs['fuel'] = bunker_cost
        
        # 3. Crew costs
        costs['crew'] = profile['crew_cost'] * voyage_days
        
        # 4. Port handling
        costs['port_handling'] = profile['handling_cost'] * dwt_used
        
        # 5. Canal fees (if applicable)
        if self._requires_suez_canal(origin_port):
            costs['canal_fees'] = self.market_conditions['suez_canal_fee']
        else:
            costs['canal_fees'] = 0
        
        # 6. Insurance
        costs['insurance'] = cargo_value_usd * self.market_conditions['insurance_rate']
        
        # 7. Regional risk premium
        risk_premium = self.region_factors.get(region, self.region_factors['default'])['risk_premium']
        base_cost = sum(costs.values())
        costs['risk_premium'] = base_cost * risk_premium
        
        # 8. Red Sea surcharge (if applicable)
        if self._requires_red_sea_transit(origin_port):
            costs['red_sea_surcharge'] = base_cost * self.market_conditions['red_sea_surcharge']
        else:
            costs['red_sea_surcharge'] = 0
        
        # Total cost
        total_cost = sum(costs.values())
        cost_per_ton = total_cost / dwt_used
        
        # Apply regional adjustment
        region_adj = self.region_factors.get(region, self.region_factors['default'])[cargo_type.split('_')[0]]
        final_cost_per_ton = cost_per_ton * region_adj
        
        return {
            'origin_port': origin_port,
            'country': country,
            'region': region,
            'cargo_type': cargo_type,
            'voyage_days': voyage_days,
            'dwt_used': dwt_used,
            'cost_breakdown': costs,
            'total_cost_usd': total_cost * region_adj,
            'cost_per_ton_usd': round(final_cost_per_ton, 2),
            'drewry_rate': drewry_rate,
            'baltic_rate': baltic_rate,
            'calculation_date': datetime.now().isoformat()
        }
    
    def _get_optimal_baltic_rate(self, cargo_type: str, dwt_used: int) -> float:
        """Select optimal Baltic index based on cargo type and vessel size"""
        if self.baltic_data.empty:
            return 8000  # Default rate
        
        # Selection logic based on vessel size and cargo type
        if cargo_type == 'General_Cargo':
            if dwt_used <= 10000 and 'BHSI' in self.baltic_data.columns:
                return self.baltic_data['BHSI'].iloc[-1]['value']
            elif dwt_used <= 20000 and 'BSI' in self.baltic_data.columns:
                return self.baltic_data['BSI'].iloc[-1]['value']
        elif cargo_type == 'Project_Cargo':
            if dwt_used <= 15000 and 'BSI' in self.baltic_data.columns:
                return self.baltic_data['BSI'].iloc[-1]['value']
            elif dwt_used <= 25000 and 'BPI' in self.baltic_data.columns:
                return self.baltic_data['BPI'].iloc[-1]['value']
        
        # Fallback hierarchy
        for index in ['BSI', 'BDI', 'BPI', 'BCI', 'BHSI']:
            if index in self.baltic_data.columns:
                return self.baltic_data[index].iloc[-1]['value']
        
        return 8000  # Default rate
    
    def _map_region(self, port: str) -> str:
        """Map port to regional factor category"""
        port_data = self.port_manager.ports_data.get(port, {})
        return port_data.get('region', 'default')
    
    def _requires_suez_canal(self, port: str) -> bool:
        """Check if route requires Suez Canal transit"""
        # Ports that typically require Suez Canal
        suez_ports = ['Tianjin', 'Shanghai', 'Rizhao', 'Qingdao', 'Dalian', 'Ningbo', 
                     'Novorossiysk', 'Sines', 'Leixões']
        return port in suez_ports
    
    def _requires_red_sea_transit(self, port: str) -> bool:
        """Check if route requires Red Sea transit (higher risk)"""
        # Currently mainly China ports due to geopolitical situation
        red_sea_ports = ['Tianjin', 'Shanghai', 'Rizhao', 'Qingdao', 'Dalian', 'Ningbo']
        return port in red_sea_ports
    
    def calculate_multiple_routes(self, ports: list = None, cargo_types: list = None) -> pd.DataFrame:
        """Calculate costs for multiple routes and return as DataFrame"""
        if ports is None:
            ports = list(self.voyage_days_map.keys())
        if cargo_types is None:
            cargo_types = ['General_Cargo', 'Project_Cargo']
        
        results = []
        for port in ports:
            for cargo_type in cargo_types:
                try:
                    result = self.calculate_comprehensive_cost(port, cargo_type)
                    results.append({
                        'Origin_Port': result['origin_port'],
                        'Country': result['country'],
                        'Region': result['region'],
                        'Cargo_Type': result['cargo_type'],
                        'Voyage_Days': result['voyage_days'],
                        'Cost_Per_Ton_USD': result['cost_per_ton_usd'],
                        'Total_Cost_USD': result['total_cost_usd'],
                        'Vessel_Charter': result['cost_breakdown']['vessel_charter'],
                        'Fuel_Cost': result['cost_breakdown']['fuel'],
                        'Port_Handling': result['cost_breakdown']['port_handling'],
                        'Canal_Fees': result['cost_breakdown']['canal_fees'],
                        'Insurance': result['cost_breakdown']['insurance'],
                        'Risk_Premium': result['cost_breakdown']['risk_premium'],
                        'Red_Sea_Surcharge': result['cost_breakdown']['red_sea_surcharge'],
                        'Calculation_Date': datetime.now().date()
                    })
                except Exception as e:
                    print(f"[ERROR] Failed to calculate for {port} - {cargo_type}: {e}")
                    continue
        
        return pd.DataFrame(results)

if __name__ == "__main__":
    print("Enhanced Shipping Cost Calculator")
    print("=" * 50)
    
    calculator = EnhancedShippingCalculator()
    
    # Calculate for all available ports
    results_df = calculator.calculate_multiple_routes()
    
    if not results_df.empty:
        print(f"\nCalculated costs for {len(results_df)} route combinations")
        print(f"Most competitive routes:")
        top_5 = results_df.nsmallest(5, 'Cost_Per_Ton_USD')
        for _, row in top_5.iterrows():
            print(f"  {row['Origin_Port']} ({row['Country']}) - {row['Cargo_Type']}: ${row['Cost_Per_Ton_USD']:.2f}/ton")
        
        # Save results
        output_dir = Path(r"C:\Users\<USER>\Code\shipment\reports")
        output_dir.mkdir(exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_df.to_csv(output_dir / f"enhanced_shipping_costs_{timestamp}.csv", index=False)
        print(f"\nDetailed results saved to: {output_dir}")
    else:
        print("No results generated. Check data availability.")
