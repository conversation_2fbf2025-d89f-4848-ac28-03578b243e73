from playwright.sync_api import sync_playwright
import json
import csv
import os
from datetime import datetime
from pathlib import Path
import re

class KlineDataExtractor:
    def __init__(self):
        self.base_url = 'https://www.kline.co.jp/en/ir/finance/shipping.html'
        self.output_dir = Path('data')
        self.charts_dir = self.output_dir / 'charts_data'
        self.indices_dir = self.output_dir / 'shipping_indices'
        self.raw_dir = self.output_dir / 'raw_extracts'
        
        # Create directories
        for dir_path in [self.output_dir, self.charts_dir, self.indices_dir, self.raw_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
    
    def extract_all_data(self):
        """
        Extract all chart data from the Kline shipping page
        """
        print("🚢 Kline Complete Data Extractor")
        print("=" * 60)
        
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=False)
            context = browser.new_context()
            page = context.new_page()
            
            print(f"📡 Loading page: {self.base_url}")
            page.goto(self.base_url, wait_until='networkidle')
            page.wait_for_timeout(5000)  # Wait for charts to load
            
            # Extract chart data from Highcharts runtime
            charts_data = self._extract_highcharts_data(page)
            
            # Save raw chart data
            raw_file = self.raw_dir / 'complete_charts_data.json'
            with open(raw_file, 'w') as f:
                json.dump(charts_data, f, indent=2)
            print(f"💾 Saved raw chart data: {raw_file}")
            
            # Process and organize data
            self._process_all_charts(charts_data)
            
            # Extract any table data
            self._extract_table_data(page)
            
            browser.close()
        
        print("\n✅ Data extraction complete!")
        self._generate_summary()
    
    def _extract_highcharts_data(self, page):
        """Extract data from all Highcharts instances"""
        print("📊 Extracting Highcharts data...")
        
        try:
            charts_data = page.evaluate('''
                () => {
                    if (typeof Highcharts !== 'undefined' && Highcharts.charts) {
                        return Highcharts.charts.map((chart, index) => {
                            if (chart && chart.series) {
                                return {
                                    index: index,
                                    title: chart.title ? chart.title.textStr : `Chart ${index}`,
                                    subtitle: chart.subtitle ? chart.subtitle.textStr : '',
                                    yAxis: chart.yAxis ? chart.yAxis.map(axis => ({
                                        title: axis.options.title ? axis.options.title.text : '',
                                        min: axis.min,
                                        max: axis.max
                                    })) : [],
                                    xAxis: chart.xAxis ? chart.xAxis.map(axis => ({
                                        title: axis.options.title ? axis.options.title.text : '',
                                        categories: axis.categories || [],
                                        type: axis.options.type || 'linear'
                                    })) : [],
                                    series: chart.series.map(s => ({
                                        name: s.name,
                                        type: s.type,
                                        color: s.color,
                                        yAxis: s.yAxis ? s.yAxis.options.index : 0,
                                        data: s.data ? s.data.map(point => {
                                            if (point && typeof point === 'object') {
                                                return {
                                                    x: point.x !== undefined ? point.x : point.index,
                                                    y: point.y,
                                                    category: point.category || point.name || null,
                                                    name: point.name || null
                                                };
                                            }
                                            return { x: null, y: point, category: null, name: null };
                                        }) : []
                                    }))
                                };
                            }
                            return null;
                        }).filter(chart => chart !== null);
                    }
                    return [];
                }
            ''')
            
            print(f"📈 Found {len(charts_data)} charts")
            return charts_data
            
        except Exception as e:
            print(f"❌ Error extracting Highcharts data: {e}")
            return []
    
    def _process_all_charts(self, charts_data):
        """Process and save data from all charts"""
        print("\n🔄 Processing chart data...")
        
        chart_summary = []
        
        for chart in charts_data:
            chart_info = {
                'index': chart['index'],
                'title': chart['title'],
                'subtitle': chart['subtitle'],
                'series_count': len(chart['series']),
                'series_names': [s['name'] for s in chart['series']],
                'data_files': []
            }
            
            print(f"\n📊 Chart {chart['index']}: {chart['title']}")
            if chart['subtitle']:
                print(f"   Subtitle: {chart['subtitle']}")
            
            # Create chart-specific folder
            chart_folder = self.charts_dir / f"chart_{chart['index']:02d}_{self._sanitize_filename(chart['title'])}"
            chart_folder.mkdir(exist_ok=True)
            
            # Save chart metadata
            metadata_file = chart_folder / 'chart_metadata.json'
            with open(metadata_file, 'w') as f:
                json.dump({
                    'title': chart['title'],
                    'subtitle': chart['subtitle'],
                    'yAxis': chart['yAxis'],
                    'xAxis': chart['xAxis'],
                    'series_info': [{'name': s['name'], 'type': s['type'], 'color': s['color']} 
                                   for s in chart['series']]
                }, f, indent=2)
            
            # Process each series
            for series in chart['series']:
                if series['data']:
                    # Filter out null values
                    valid_data = [point for point in series['data'] if point['y'] is not None]
                    
                    if valid_data:
                        # Save series data
                        series_filename = f"{self._sanitize_filename(series['name'])}_data.csv"
                        series_file = chart_folder / series_filename
                        
                        with open(series_file, 'w', newline='') as f:
                            writer = csv.DictWriter(f, fieldnames=['date', 'value', 'x_index', 'category'])
                            writer.writeheader()
                            
                            for point in valid_data:
                                writer.writerow({
                                    'date': point['category'] or f"Point_{point['x']}",
                                    'value': point['y'],
                                    'x_index': point['x'],
                                    'category': point['category']
                                })
                        
                        chart_info['data_files'].append(series_filename)
                        print(f"   💾 {series['name']}: {len(valid_data)} data points → {series_filename}")
                        
                        # Copy important shipping indices to main indices folder
                        if self._is_shipping_index(series['name'], chart['title']):
                            main_file = self.indices_dir / series_filename
                            with open(main_file, 'w', newline='') as f:
                                writer = csv.DictWriter(f, fieldnames=['date', 'value'])
                                writer.writeheader()
                                for point in valid_data:
                                    writer.writerow({
                                        'date': point['category'] or f"Point_{point['x']}",
                                        'value': point['y']
                                    })
                            print(f"   📋 Copied to main indices: {series_filename}")
            
            chart_summary.append(chart_info)
        
        # Save overall summary
        summary_file = self.charts_dir / 'charts_summary.json'
        with open(summary_file, 'w') as f:
            json.dump(chart_summary, f, indent=2)
        
        return chart_summary
    
    def _extract_table_data(self, page):
        """Extract any HTML table data from the page"""
        print("\n🗂️  Looking for HTML tables...")
        
        try:
            tables_data = page.evaluate('''
                () => {
                    const tables = document.querySelectorAll('table');
                    return Array.from(tables).map((table, index) => {
                        const rows = Array.from(table.querySelectorAll('tr'));
                        return {
                            index: index,
                            headers: Array.from(rows[0]?.querySelectorAll('th, td') || []).map(cell => cell.textContent.trim()),
                            data: rows.slice(1).map(row => 
                                Array.from(row.querySelectorAll('td')).map(cell => cell.textContent.trim())
                            ).filter(row => row.length > 0)
                        };
                    }).filter(table => table.data.length > 0);
                }
            ''')
            
            if tables_data:
                print(f"📋 Found {len(tables_data)} tables")
                
                for table in tables_data:
                    table_file = self.raw_dir / f'table_{table["index"]}_data.csv'
                    
                    with open(table_file, 'w', newline='', encoding='utf-8') as f:
                        writer = csv.writer(f)
                        if table['headers']:
                            writer.writerow(table['headers'])
                        writer.writerows(table['data'])
                    
                    print(f"   💾 Table {table['index']}: {len(table['data'])} rows → {table_file.name}")
            else:
                print("   ℹ️  No HTML tables found")
                
        except Exception as e:
            print(f"❌ Error extracting table data: {e}")
    
    def _is_shipping_index(self, series_name, chart_title):
        """Check if a series is a major shipping index"""
        shipping_keywords = ['BSI', 'BDI', 'BCI', 'BPI', 'BHSI', 'VLCC', 'Aframax', 'WS', 'SCFI']
        return any(keyword in series_name.upper() for keyword in shipping_keywords)
    
    def _sanitize_filename(self, filename):
        """Sanitize filename for cross-platform compatibility"""
        # Remove or replace invalid characters
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        filename = re.sub(r'[（）\(\)]', '', filename)
        filename = re.sub(r'\s+', '_', filename)
        filename = filename.strip('_')
        return filename[:50]  # Limit length
    
    def _generate_summary(self):
        """Generate a comprehensive summary of extracted data"""
        print("\n📋 Generating summary...")
        
        summary = {
            'extraction_date': datetime.now().isoformat(),
            'source_url': self.base_url,
            'folders': {},
            'file_counts': {},
            'shipping_indices': []
        }
        
        # Count files in each directory
        for dir_name, dir_path in [
            ('charts_data', self.charts_dir),
            ('shipping_indices', self.indices_dir), 
            ('raw_extracts', self.raw_dir)
        ]:
            if dir_path.exists():
                files = list(dir_path.rglob('*'))
                csv_files = [f for f in files if f.suffix == '.csv']
                json_files = [f for f in files if f.suffix == '.json']
                
                summary['folders'][dir_name] = str(dir_path)
                summary['file_counts'][dir_name] = {
                    'total_files': len(files),
                    'csv_files': len(csv_files),
                    'json_files': len(json_files)
                }
        
        # List shipping indices
        if self.indices_dir.exists():
            for csv_file in self.indices_dir.glob('*.csv'):
                summary['shipping_indices'].append(csv_file.name)
        
        # Save summary
        summary_file = self.output_dir / 'extraction_summary.json'
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2)
        
        print(f"📄 Summary saved: {summary_file}")
        
        # Print summary to console
        print(f"\n📊 EXTRACTION SUMMARY")
        print(f"{'='*50}")
        print(f"📅 Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🌐 Source: {self.base_url}")
        print(f"\n📁 Files created:")
        for folder, counts in summary['file_counts'].items():
            print(f"   {folder}: {counts['csv_files']} CSV, {counts['json_files']} JSON")
        
        print(f"\n🚢 Shipping Indices Available:")
        for index in summary['shipping_indices']:
            print(f"   • {index}")

if __name__ == "__main__":
    extractor = KlineDataExtractor()
    extractor.extract_all_data()
