import pandas as pd
import json
from pathlib import Path
from datetime import datetime
import requests
from typing import Dict, List, Tuple, Optional

class PortDatabaseManager:
    """
    Manages port database for shipping cost calculations to Israel
    Handles port data, voyage times, and regional classifications
    """
    
    def __init__(self):
        self.data_path = Path(r"C:\Users\<USER>\Code\shipment\data")
        self.ports_db_path = self.data_path / "ports_database"
        self.ports_db_path.mkdir(exist_ok=True)
        
        # Initialize with current known ports
        self.ports_data = self._load_existing_ports()
    
    def _load_existing_ports(self) -> Dict:
        """Load existing port data from the current system"""
        return {
            # China ports
            "Tianjin": {
                "country": "China",
                "region": "China",
                "voyage_days": 52.4,
                "coordinates": {"lat": 39.1422, "lon": 117.1767},
                "major_commodities": ["steel", "rebar", "machinery", "containers"],
                "port_type": "major_commercial",
                "handling_capabilities": ["breakbulk", "container", "bulk"]
            },
            "Rizhao": {
                "country": "China",
                "region": "China", 
                "voyage_days": 51.6,
                "coordinates": {"lat": 35.4164, "lon": 119.4611},
                "major_commodities": ["iron_ore", "steel", "coal"],
                "port_type": "bulk_specialist",
                "handling_capabilities": ["bulk", "breakbulk"]
            },
            "Shanghai": {
                "country": "China",
                "region": "China",
                "voyage_days": 51.5,
                "coordinates": {"lat": 31.2304, "lon": 121.4737},
                "major_commodities": ["containers", "steel", "machinery", "general_cargo"],
                "port_type": "major_commercial",
                "handling_capabilities": ["container", "breakbulk", "bulk"]
            },
            "Qingdao": {
                "country": "China",
                "region": "China",
                "voyage_days": 51.8,
                "coordinates": {"lat": 36.0986, "lon": 120.3719},
                "major_commodities": ["containers", "iron_ore", "crude_oil"],
                "port_type": "major_commercial",
                "handling_capabilities": ["container", "bulk", "breakbulk"]
            },
            "Dalian": {
                "country": "China",
                "region": "China",
                "voyage_days": 52.0,
                "coordinates": {"lat": 38.9140, "lon": 121.6147},
                "major_commodities": ["crude_oil", "containers", "grain"],
                "port_type": "major_commercial",
                "handling_capabilities": ["container", "bulk", "breakbulk", "tanker"]
            },
            "Ningbo": {
                "country": "China",
                "region": "China",
                "voyage_days": 51.2,
                "coordinates": {"lat": 29.8683, "lon": 121.5440},
                "major_commodities": ["containers", "iron_ore", "crude_oil"],
                "port_type": "major_commercial",
                "handling_capabilities": ["container", "bulk", "breakbulk"]
            },
            
            # Russia ports
            "Novorossiysk": {
                "country": "Russia",
                "region": "Russia",
                "voyage_days": 9.0,
                "coordinates": {"lat": 44.7209, "lon": 37.7854},
                "major_commodities": ["grain", "crude_oil", "steel"],
                "port_type": "major_commercial",
                "handling_capabilities": ["bulk", "breakbulk", "tanker"]
            },
            
            # Turkey ports
            "Iskenderun": {
                "country": "Turkey",
                "region": "Turkey",
                "voyage_days": 5.4,
                "coordinates": {"lat": 36.5876, "lon": 36.1744},
                "major_commodities": ["steel", "containers", "general_cargo"],
                "port_type": "regional_commercial",
                "handling_capabilities": ["container", "breakbulk", "bulk"]
            },
            "Mersin": {
                "country": "Turkey", 
                "region": "Turkey",
                "voyage_days": 5.0,
                "coordinates": {"lat": 36.8121, "lon": 34.6415},
                "major_commodities": ["containers", "general_cargo", "grain"],
                "port_type": "major_commercial",
                "handling_capabilities": ["container", "breakbulk", "bulk"]
            },
            "Nemrut Bay": {
                "country": "Turkey",
                "region": "Turkey", 
                "voyage_days": 7.0,
                "coordinates": {"lat": 38.7500, "lon": 38.0000},
                "major_commodities": ["crude_oil", "petroleum_products"],
                "port_type": "oil_terminal",
                "handling_capabilities": ["tanker", "breakbulk"]
            },
            "Izmir": {
                "country": "Turkey",
                "region": "Turkey",
                "voyage_days": 6.5,
                "coordinates": {"lat": 38.4192, "lon": 27.1287},
                "major_commodities": ["containers", "general_cargo", "grain"],
                "port_type": "major_commercial",
                "handling_capabilities": ["container", "breakbulk", "bulk"]
            },
            "Aliaga": {
                "country": "Turkey",
                "region": "Turkey",
                "voyage_days": 6.8,
                "coordinates": {"lat": 38.8000, "lon": 26.9667},
                "major_commodities": ["crude_oil", "petroleum_products", "chemicals"],
                "port_type": "industrial_specialist",
                "handling_capabilities": ["tanker", "breakbulk", "bulk"]
            },
            
            # Portugal ports
            "Sines": {
                "country": "Portugal",
                "region": "Portugal",
                "voyage_days": 25.8,
                "coordinates": {"lat": 37.9553, "lon": -8.8672},
                "major_commodities": ["crude_oil", "containers", "coal"],
                "port_type": "major_commercial",
                "handling_capabilities": ["tanker", "container", "bulk", "breakbulk"]
            },
            "Leixões": {
                "country": "Portugal",
                "region": "Portugal", 
                "voyage_days": 26.5,
                "coordinates": {"lat": 41.1844, "lon": -8.7019},
                "major_commodities": ["containers", "general_cargo", "fuel"],
                "port_type": "major_commercial",
                "handling_capabilities": ["container", "breakbulk", "bulk"]
            }
        }
    
    def add_port(self, port_name: str, country: str, region: str, 
                 voyage_days: float, coordinates: Dict[str, float],
                 major_commodities: List[str], port_type: str,
                 handling_capabilities: List[str]) -> bool:
        """Add a new port to the database"""
        try:
            self.ports_data[port_name] = {
                "country": country,
                "region": region,
                "voyage_days": voyage_days,
                "coordinates": coordinates,
                "major_commodities": major_commodities,
                "port_type": port_type,
                "handling_capabilities": handling_capabilities,
                "added_date": datetime.now().isoformat()
            }
            self.save_ports_database()
            print(f"[INFO] Successfully added port: {port_name}")
            return True
        except Exception as e:
            print(f"[ERROR] Failed to add port {port_name}: {e}")
            return False
    
    def get_ports_by_region(self, region: str) -> Dict:
        """Get all ports in a specific region"""
        return {name: data for name, data in self.ports_data.items() 
                if data['region'] == region}
    
    def get_ports_by_capability(self, capability: str) -> Dict:
        """Get all ports with specific handling capability"""
        return {name: data for name, data in self.ports_data.items()
                if capability in data['handling_capabilities']}
    
    def estimate_voyage_time(self, port_name: str, distance_nm: float, 
                           vessel_speed_knots: float = 12.0) -> float:
        """Estimate voyage time based on distance and vessel speed"""
        # Basic calculation: distance / speed + port time
        sea_time = distance_nm / (vessel_speed_knots * 24)  # Convert to days
        port_time = 4.0  # Standard port time assumption
        return round(sea_time * 2 + port_time, 1)  # Round trip + port time
    
    def save_ports_database(self):
        """Save ports database to JSON file"""
        db_file = self.ports_db_path / "ports_database.json"
        with open(db_file, 'w') as f:
            json.dump(self.ports_data, f, indent=2, default=str)
        print(f"[INFO] Ports database saved to: {db_file}")
    
    def load_ports_database(self) -> Dict:
        """Load ports database from JSON file"""
        db_file = self.ports_db_path / "ports_database.json"
        if db_file.exists():
            with open(db_file, 'r') as f:
                return json.load(f)
        return {}
    
    def export_for_cost_engine(self) -> Tuple[Dict, Dict]:
        """Export data in format needed by BreakbulkCostEngine"""
        voyage_days_map = {}
        port_to_country = {}
        
        for port_name, port_data in self.ports_data.items():
            voyage_days_map[port_name] = port_data['voyage_days']
            port_to_country[port_name] = port_data['country']
        
        return voyage_days_map, port_to_country
    
    def generate_port_report(self) -> pd.DataFrame:
        """Generate comprehensive port report"""
        report_data = []
        for port_name, port_data in self.ports_data.items():
            report_data.append({
                'Port_Name': port_name,
                'Country': port_data['country'],
                'Region': port_data['region'],
                'Voyage_Days_to_Israel': port_data['voyage_days'],
                'Latitude': port_data['coordinates']['lat'],
                'Longitude': port_data['coordinates']['lon'],
                'Port_Type': port_data['port_type'],
                'Handling_Capabilities': ', '.join(port_data['handling_capabilities']),
                'Major_Commodities': ', '.join(port_data['major_commodities'])
            })
        
        return pd.DataFrame(report_data)

if __name__ == "__main__":
    # Example usage
    port_manager = PortDatabaseManager()
    
    # Generate and save port report
    report = port_manager.generate_port_report()
    output_dir = Path(r"C:\Users\<USER>\Code\shipment\reports")
    output_dir.mkdir(exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report.to_csv(output_dir / f"ports_database_report_{timestamp}.csv", index=False)
    
    print(f"Port Database Report Generated:")
    print(f"Total ports: {len(report)}")
    print(f"Countries covered: {report['Country'].nunique()}")
    print(f"Regions covered: {report['Region'].nunique()}")
    print(f"\nReport saved to: {output_dir}")
    
    # Save the database
    port_manager.save_ports_database()
