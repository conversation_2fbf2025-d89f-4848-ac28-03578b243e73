import json
import pandas as pd
from datetime import datetime
from pathlib import Path

def convert_lme_json_to_table():
    """
    Convert the captured LME JSON data to a clean table format
    """
    print("🔄 Converting LME JSON to Table")
    print("=" * 40)
    
    # Load the specific JSON file
    json_file = Path('data/lme_raw_extracts/lme_json_responses_20250708_120447.json')
    
    if not json_file.exists():
        print(f"❌ File not found: {json_file}")
        return
    
    try:
        with open(json_file, 'r') as f:
            data = json.load(f)
        
        print(f"📁 Processing: {json_file.name}")
        
        # Find the chart data response
        chart_data = None
        for response in data:
            if 'chart-data' in response.get('url', ''):
                chart_data = response['data']
                break
        
        if not chart_data:
            print("❌ No chart data found in JSON")
            return
        
        # Extract dates and price datasets
        labels = chart_data.get('Labels', [])
        datasets = chart_data.get('Datasets', [])
        
        print(f"📊 Found {len(labels)} dates and {len(datasets)} datasets")
        
        # Create a comprehensive table
        all_data = []
        
        # Process each dataset (different contract months)
        for dataset in datasets:
            if dataset.get('Label') == 'Price':
                row_title = dataset.get('RowTitle', '')
                prices = dataset.get('Data', [])
                hover_info = dataset.get('Hover', [])
                
                print(f"💰 Processing: {row_title}")
                
                # Combine dates with prices
                for i, (date_str, price_str) in enumerate(zip(labels, prices)):
                    try:
                        # Parse date - try different formats
                        date_obj = None
                        for date_format in ['%m/%d/%Y', '%d/%m/%Y']:
                            try:
                                date_obj = datetime.strptime(date_str, date_format)
                                break
                            except ValueError:
                                continue
                        
                        if not date_obj:
                            print(f"⚠️ Could not parse date: {date_str}")
                            continue
                        
                        formatted_date = date_obj.strftime('%Y-%m-%d')
                        price_value = float(price_str)
                        
                        # Get hover info if available
                        hover = hover_info[i] if i < len(hover_info) else ''
                        
                        all_data.append({
                            'date': formatted_date,
                            'price_usd_ton': price_value,
                            'contract': row_title,
                            'hover_info': hover,
                            'source': 'LME_Steel_Rebar_FOB_Turkey'
                        })
                        
                    except (ValueError, TypeError) as e:
                        print(f"⚠️ Error processing {date_str}, {price_str}: {e}")
                        continue
        
        if not all_data:
            print("❌ No valid data extracted")
            return
        
        # Create DataFrame and sort by date
        df = pd.DataFrame(all_data)
        df = df.sort_values(['date', 'contract'])
        
        print(f"✅ Extracted {len(df)} data points")
        
        # Show data summary
        latest_date = df['date'].max()
        latest_prices = df[df['date'] == latest_date]
        
        print(f"\n📈 Latest Prices ({latest_date}):")
        for _, row in latest_prices.iterrows():
            print(f"  {row['contract']}: ${row['price_usd_ton']:.2f}/ton")
        
        # Save the data
        output_dir = Path('data/lme_data')
        output_dir.mkdir(exist_ok=True)
        
        # Save full dataset
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        full_file = output_dir / f'lme_steel_rebar_full_{timestamp}.csv'
        df.to_csv(full_file, index=False)
        print(f"💾 Saved full dataset: {full_file}")
        
        # Save latest prices only (most recent contract)
        latest_contract_data = df[df['date'] == latest_date].iloc[-1]  # Get last contract for latest date
        
        latest_df = pd.DataFrame([{
            'date': latest_contract_data['date'],
            'price_usd_ton': latest_contract_data['price_usd_ton'],
            'source': 'LME_Steel_Rebar_FOB_Turkey'
        }])
        
        latest_file = output_dir / 'latest_steel_price.csv'
        latest_df.to_csv(latest_file, index=False)
        print(f"💾 Saved latest price: {latest_file}")
        
        # Save in standard format for rebar calculator
        standard_df = df[['date', 'price_usd_ton']].copy()
        standard_df.columns = ['date', 'value']
        standard_df['source'] = 'LME_Steel_Rebar_FOB_Turkey'
        
        # Remove duplicates by taking the average price per date
        standard_df = standard_df.groupby('date').agg({
            'value': 'mean',
            'source': 'first'
        }).reset_index()
        
        standard_file = output_dir / 'LME_Steel_Rebar_data.csv'
        standard_df.to_csv(standard_file, index=False)
        print(f"💾 Saved standard format: {standard_file}")
        
        # Print summary for rebar calculator
        print(f"\n🎯 READY FOR REBAR CALCULATOR:")
        print(f"Latest FOB Price: ${latest_contract_data['price_usd_ton']:.2f}/ton")
        print(f"Date: {latest_contract_data['date']}")
        print(f"Contract: {latest_contract_data['contract']}")
        
        print(f"\n💡 Now run the rebar calculator:")
        print(f"   python cal-market-price/rebar_cli.py compare --tonnage 5000")
        
        return df
        
    except Exception as e:
        print(f"❌ Error processing JSON: {e}")
        return None

if __name__ == "__main__":
    convert_lme_json_to_table()
