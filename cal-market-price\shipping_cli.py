#!/usr/bin/env python3
"""
Command Line Interface for Shipping Cost Calculator
Provides easy access to shipping cost calculations for Israel routes
"""

import argparse
import sys
from pathlib import Path
import json
from datetime import datetime
import pandas as pd

# Add current directory to path for imports
sys.path.append(str(Path(__file__).parent))

from enhanced_shipping_calculator import EnhancedShippingCalculator
from port_database_manager import PortDatabaseManager

def main():
    parser = argparse.ArgumentParser(
        description='Calculate breakbulk shipping costs to Israel',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Get quote for Shanghai to Israel
  python shipping_cli.py quote --port Shanghai --cargo General_Cargo

  # Compare multiple ports
  python shipping_cli.py compare --ports Shanghai Tianjin Mersin --cargo Project_Cargo

  # List all available ports
  python shipping_cli.py ports

  # Get top 5 most competitive routes
  python shipping_cli.py top --limit 5

  # Export all calculations to CSV
  python shipping_cli.py export --output my_shipping_costs.csv
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Quote command
    quote_parser = subparsers.add_parser('quote', help='Get shipping quote for specific port')
    quote_parser.add_argument('--port', required=True, help='Origin port name')
    quote_parser.add_argument('--cargo', choices=['General_Cargo', 'Project_Cargo'], 
                             default='General_Cargo', help='Cargo type')
    quote_parser.add_argument('--dwt', type=int, default=5000, help='DWT used (tons)')
    quote_parser.add_argument('--value', type=float, default=1000000, help='Cargo value (USD)')
    quote_parser.add_argument('--detailed', action='store_true', help='Show detailed cost breakdown')
    
    # Compare command
    compare_parser = subparsers.add_parser('compare', help='Compare multiple ports')
    compare_parser.add_argument('--ports', nargs='+', required=True, help='List of port names')
    compare_parser.add_argument('--cargo', choices=['General_Cargo', 'Project_Cargo'], 
                               default='General_Cargo', help='Cargo type')
    compare_parser.add_argument('--dwt', type=int, default=5000, help='DWT used (tons)')
    
    # Ports command
    ports_parser = subparsers.add_parser('ports', help='List available ports')
    ports_parser.add_argument('--region', help='Filter by region')
    ports_parser.add_argument('--capability', help='Filter by handling capability')
    ports_parser.add_argument('--format', choices=['table', 'json'], default='table', 
                             help='Output format')
    
    # Top command
    top_parser = subparsers.add_parser('top', help='Show most competitive routes')
    top_parser.add_argument('--limit', type=int, default=10, help='Number of routes to show')
    top_parser.add_argument('--cargo', choices=['General_Cargo', 'Project_Cargo'], 
                           help='Filter by cargo type')
    
    # Export command
    export_parser = subparsers.add_parser('export', help='Export all calculations to file')
    export_parser.add_argument('--output', help='Output filename (default: auto-generated)')
    export_parser.add_argument('--format', choices=['csv', 'json', 'excel'], default='csv', 
                              help='Export format')
    
    # Market command
    market_parser = subparsers.add_parser('market', help='Show current market data')
    market_parser.add_argument('--indices', choices=['baltic', 'drewry', 'all'], default='all',
                              help='Which indices to show')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # Initialize calculator and port manager
    try:
        print("Initializing shipping calculator...")
        calculator = EnhancedShippingCalculator()
        port_manager = PortDatabaseManager()
        print("✓ Ready")
    except Exception as e:
        print(f"Error initializing calculator: {e}")
        return
    
    # Execute commands
    try:
        if args.command == 'quote':
            handle_quote_command(calculator, args)
        elif args.command == 'compare':
            handle_compare_command(calculator, args)
        elif args.command == 'ports':
            handle_ports_command(port_manager, args)
        elif args.command == 'top':
            handle_top_command(calculator, args)
        elif args.command == 'export':
            handle_export_command(calculator, args)
        elif args.command == 'market':
            handle_market_command(calculator, args)
    except Exception as e:
        print(f"Error executing command: {e}")

def handle_quote_command(calculator, args):
    """Handle quote command"""
    print(f"\nCalculating shipping cost from {args.port} to Israel...")
    print("-" * 50)
    
    try:
        result = calculator.calculate_comprehensive_cost(
            origin_port=args.port,
            cargo_type=args.cargo,
            dwt_used=args.dwt,
            cargo_value_usd=args.value
        )
        
        print(f"Origin Port: {result['origin_port']} ({result['country']})")
        print(f"Cargo Type: {result['cargo_type']}")
        print(f"Voyage Days: {result['voyage_days']}")
        print(f"DWT Used: {result['dwt_used']:,} tons")
        print(f"\n💰 COST PER TON: ${result['cost_per_ton_usd']:.2f}")
        print(f"💰 TOTAL COST: ${result['total_cost_usd']:,.2f}")
        
        if args.detailed:
            print(f"\n📊 DETAILED BREAKDOWN:")
            for cost_type, amount in result['cost_breakdown'].items():
                print(f"  {cost_type.replace('_', ' ').title()}: ${amount:,.2f}")
        
        print(f"\n📈 Market Rates Used:")
        print(f"  Drewry Index: {result['drewry_rate']:.1f}")
        print(f"  Baltic Index: {result['baltic_rate']:.1f}")
        
    except ValueError as e:
        print(f"❌ Error: {e}")
        available_ports = list(calculator.voyage_days_map.keys())
        print(f"Available ports: {', '.join(available_ports[:10])}...")

def handle_compare_command(calculator, args):
    """Handle compare command"""
    print(f"\nComparing shipping costs for {args.cargo}...")
    print("-" * 50)
    
    results = []
    for port in args.ports:
        try:
            result = calculator.calculate_comprehensive_cost(
                origin_port=port,
                cargo_type=args.cargo,
                dwt_used=args.dwt
            )
            results.append(result)
        except Exception as e:
            print(f"❌ {port}: Error - {e}")
    
    if results:
        # Sort by cost per ton
        results.sort(key=lambda x: x['cost_per_ton_usd'])
        
        print(f"\n🏆 COMPARISON RESULTS (sorted by cost per ton):")
        print(f"{'Rank':<4} {'Port':<15} {'Country':<12} {'Days':<6} {'$/ton':<10} {'Total Cost':<12}")
        print("-" * 65)
        
        for i, result in enumerate(results, 1):
            print(f"{i:<4} {result['origin_port']:<15} {result['country']:<12} "
                  f"{result['voyage_days']:<6.1f} ${result['cost_per_ton_usd']:<9.2f} "
                  f"${result['total_cost_usd']:<11,.0f}")
        
        best = results[0]
        print(f"\n🥇 BEST OPTION: {best['origin_port']} at ${best['cost_per_ton_usd']:.2f}/ton")

def handle_ports_command(port_manager, args):
    """Handle ports command"""
    ports_data = port_manager.ports_data
    
    # Apply filters
    if args.region:
        ports_data = {k: v for k, v in ports_data.items() if v['region'] == args.region}
    
    if args.capability:
        ports_data = {k: v for k, v in ports_data.items() 
                     if args.capability in v['handling_capabilities']}
    
    if not ports_data:
        print("No ports found matching the criteria.")
        return
    
    if args.format == 'json':
        print(json.dumps(ports_data, indent=2, default=str))
    else:
        print(f"\n📍 AVAILABLE PORTS ({len(ports_data)} total):")
        print("-" * 80)
        print(f"{'Port':<15} {'Country':<12} {'Region':<15} {'Days':<6} {'Capabilities':<20}")
        print("-" * 80)
        
        for port_name, port_info in sorted(ports_data.items()):
            capabilities = ', '.join(port_info['handling_capabilities'][:3])
            if len(port_info['handling_capabilities']) > 3:
                capabilities += '...'
            
            print(f"{port_name:<15} {port_info['country']:<12} {port_info['region']:<15} "
                  f"{port_info['voyage_days']:<6.1f} {capabilities:<20}")

def handle_top_command(calculator, args):
    """Handle top command"""
    print(f"\nFinding top {args.limit} most competitive routes...")
    
    cargo_types = [args.cargo] if args.cargo else ['General_Cargo', 'Project_Cargo']
    results_df = calculator.calculate_multiple_routes(cargo_types=cargo_types)
    
    if results_df.empty:
        print("No results available.")
        return
    
    top_routes = results_df.nsmallest(args.limit, 'Cost_Per_Ton_USD')
    
    print(f"\n🏆 TOP {args.limit} MOST COMPETITIVE ROUTES:")
    print("-" * 70)
    print(f"{'Rank':<4} {'Port':<15} {'Country':<12} {'Cargo':<15} {'$/ton':<10}")
    print("-" * 70)
    
    for i, (_, row) in enumerate(top_routes.iterrows(), 1):
        print(f"{i:<4} {row['Origin_Port']:<15} {row['Country']:<12} "
              f"{row['Cargo_Type']:<15} ${row['Cost_Per_Ton_USD']:<9.2f}")

def handle_export_command(calculator, args):
    """Handle export command"""
    print("Calculating all route combinations...")
    
    results_df = calculator.calculate_multiple_routes()
    
    if results_df.empty:
        print("No data to export.")
        return
    
    # Generate filename if not provided
    if not args.output:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        args.output = f"shipping_costs_{timestamp}.{args.format}"
    
    output_path = Path("reports") / args.output
    output_path.parent.mkdir(exist_ok=True)
    
    # Export based on format
    if args.format == 'csv':
        results_df.to_csv(output_path, index=False)
    elif args.format == 'json':
        results_df.to_json(output_path, orient='records', indent=2)
    elif args.format == 'excel':
        results_df.to_excel(output_path, index=False)
    
    print(f"✓ Exported {len(results_df)} records to: {output_path}")

def handle_market_command(calculator, args):
    """Handle market command"""
    print("\n📈 CURRENT MARKET DATA:")
    print("-" * 40)
    
    if args.indices in ['baltic', 'all']:
        print("\n🌊 Baltic Indices:")
        if not calculator.baltic_data.empty:
            for col in calculator.baltic_data.columns:
                index_name = col[0] if isinstance(col, tuple) else col
                latest_value = calculator.baltic_data[col].iloc[-1]
                latest_date = calculator.baltic_data.index[-1].strftime('%Y-%m-%d')
                print(f"  {index_name}: {latest_value:.0f} (as of {latest_date})")
        else:
            print("  No Baltic data available")
    
    if args.indices in ['drewry', 'all']:
        print("\n🚢 Drewry Indices:")
        if not calculator.drewry_data.empty:
            for col in calculator.drewry_data.columns:
                index_name = col[0] if isinstance(col, tuple) else col
                latest_value = calculator.drewry_data[col].iloc[-1]
                latest_date = calculator.drewry_data.index[-1].strftime('%Y-%m-%d')
                print(f"  {index_name}: {latest_value:.1f} (as of {latest_date})")
        else:
            print("  No Drewry data available")
    
    if args.indices == 'all':
        print("\n⚙️ Market Conditions:")
        for condition, value in calculator.market_conditions.items():
            print(f"  {condition.replace('_', ' ').title()}: {value}")

if __name__ == '__main__':
    main()
