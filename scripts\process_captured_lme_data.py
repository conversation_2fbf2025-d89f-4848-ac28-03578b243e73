import json
import csv
import pandas as pd
from datetime import datetime
from pathlib import Path

class CapturedLMEProcessor:
    """
    Process the already captured LME JSON data and convert to usable price data
    """
    
    def __init__(self):
        self.output_dir = Path('data')
        self.lme_data_dir = self.output_dir / 'lme_data'
        self.raw_dir = self.output_dir / 'lme_raw_extracts'
        
        # Create directories
        for dir_path in [self.output_dir, self.lme_data_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
    
    def process_latest_capture(self):
        """
        Process the most recent captured LME JSON data
        """
        print("🔄 Processing Captured LME Data")
        print("=" * 40)
        
        # Find the most recent JSON response file
        json_files = list(self.raw_dir.glob("lme_json_responses_*.json"))
        if not json_files:
            print("❌ No captured JSON files found")
            return None
        
        latest_file = max(json_files, key=lambda x: x.stat().st_mtime)
        print(f"📁 Processing: {latest_file.name}")
        
        try:
            with open(latest_file, 'r') as f:
                captured_data = json.load(f)
            
            # Process each JSON response
            all_price_data = []
            for response in captured_data:
                if 'chart-data' in response.get('url', ''):
                    price_data = self._extract_price_data(response['data'])
                    if price_data:
                        all_price_data.extend(price_data)
            
            if all_price_data:
                self._save_processed_data(all_price_data)
                return all_price_data
            else:
                print("❌ No price data found in captured responses")
                return None
                
        except Exception as e:
            print(f"❌ Error processing captured data: {e}")
            return None
    
    def _extract_price_data(self, api_data):
        """
        Extract price data from LME API response
        """
        price_data = []
        
        try:
            # Extract labels (dates) and datasets
            labels = api_data.get('Labels', [])
            datasets = api_data.get('Datasets', [])
            
            print(f"📊 Found {len(labels)} dates and {len(datasets)} datasets")
            
            for dataset in datasets:
                if dataset.get('Label') == 'Price':
                    prices = dataset.get('Data', [])
                    row_title = dataset.get('RowTitle', '')
                    
                    print(f"💰 Processing price dataset: {row_title}")
                    print(f"📈 Price range: {len(prices)} data points")
                    
                    # Combine dates with prices
                    for i, (date_str, price_str) in enumerate(zip(labels, prices)):
                        try:
                            # Parse date (format: MM/DD/YYYY)
                            date_obj = datetime.strptime(date_str, '%m/%d/%Y')
                            formatted_date = date_obj.strftime('%Y-%m-%d')
                            
                            # Parse price
                            price_value = float(price_str)
                            
                            price_data.append({
                                'date': formatted_date,
                                'price_usd_ton': price_value,
                                'source': 'LME_Steel_Rebar_FOB_Turkey',
                                'contract': row_title,
                                'extraction_timestamp': datetime.now().isoformat()
                            })
                            
                        except (ValueError, TypeError) as e:
                            print(f"⚠️ Error parsing data point {i}: {e}")
                            continue
            
            # Sort by date
            price_data.sort(key=lambda x: x['date'])
            
            if price_data:
                latest = price_data[-1]
                earliest = price_data[0]
                print(f"🎯 Latest Price: ${latest['price_usd_ton']:.2f}/ton ({latest['date']})")
                print(f"📅 Date Range: {earliest['date']} to {latest['date']}")
                print(f"📊 Price Range: ${min(p['price_usd_ton'] for p in price_data):.2f} - ${max(p['price_usd_ton'] for p in price_data):.2f}")
            
        except Exception as e:
            print(f"❌ Error extracting price data: {e}")
        
        return price_data
    
    def _save_processed_data(self, price_data):
        """
        Save processed price data in multiple formats
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save detailed price data as CSV
        df = pd.DataFrame(price_data)
        csv_file = self.lme_data_dir / f'steel_rebar_prices_{timestamp}.csv'
        df.to_csv(csv_file, index=False)
        print(f"💾 Saved detailed prices: {csv_file}")
        
        # Save latest price for easy access by rebar calculator
        latest_price = price_data[-1]
        latest_df = pd.DataFrame([{
            'date': latest_price['date'],
            'price_usd_ton': latest_price['price_usd_ton'],
            'source': 'LME_Steel_Rebar_FOB_Turkey'
        }])
        latest_file = self.lme_data_dir / 'latest_steel_price.csv'
        latest_df.to_csv(latest_file, index=False)
        print(f"💾 Saved latest price: {latest_file}")
        
        # Save in standard format for integration with existing system
        history_df = df[['date', 'price_usd_ton']].copy()
        history_df.columns = ['date', 'value']
        history_df['source'] = 'LME_Steel_Rebar_FOB_Turkey'
        
        history_file = self.lme_data_dir / 'LME_Steel_Rebar_data.csv'
        history_df.to_csv(history_file, index=False)
        print(f"💾 Saved price history: {history_file}")
        
        # Create summary report
        summary = {
            'processing_timestamp': datetime.now().isoformat(),
            'data_points': len(price_data),
            'latest_price_usd_ton': latest_price['price_usd_ton'],
            'latest_date': latest_price['date'],
            'price_statistics': {
                'min': min(p['price_usd_ton'] for p in price_data),
                'max': max(p['price_usd_ton'] for p in price_data),
                'avg': sum(p['price_usd_ton'] for p in price_data) / len(price_data)
            },
            'date_range': {
                'start': price_data[0]['date'],
                'end': price_data[-1]['date']
            }
        }
        
        summary_file = self.lme_data_dir / f'processing_summary_{timestamp}.json'
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2, default=str)
        print(f"💾 Saved summary: {summary_file}")
        
        # Print summary
        print(f"\n📈 PROCESSING SUMMARY:")
        print(f"Data points processed: {len(price_data)}")
        print(f"Latest price: ${latest_price['price_usd_ton']:.2f}/ton")
        print(f"Date: {latest_price['date']}")
        print(f"Contract: {latest_price.get('contract', 'N/A')}")

def main():
    processor = CapturedLMEProcessor()
    
    try:
        price_data = processor.process_latest_capture()
        
        if price_data:
            print(f"\n✅ Successfully processed {len(price_data)} price data points!")
            
            # Show recent prices
            print(f"\n📊 Recent Prices (last 5 days):")
            for price_point in price_data[-5:]:
                print(f"  {price_point['date']}: ${price_point['price_usd_ton']:.2f}/ton")
                
            print(f"\n💡 Data is now available for the rebar calculator!")
            print(f"   Run: python cal-market-price/rebar_cli.py compare --tonnage 5000")
                
        else:
            print(f"\n❌ No price data processed")
            
    except Exception as e:
        print(f"\n❌ Processing failed: {e}")

if __name__ == "__main__":
    main()
