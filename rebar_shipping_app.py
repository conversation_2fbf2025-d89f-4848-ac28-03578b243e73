#!/usr/bin/env python3
"""
🏗️ REBAR SHIPPING CALCULATOR FOR ISRAEL
Main application entry point

Calculates CIF prices: FOB (LME) + Freight (Baltic) + Insurance
"""

import sys
from pathlib import Path

# Add subdirectories to path
sys.path.append(str(Path(__file__).parent / "calculators"))
sys.path.append(str(Path(__file__).parent / "scrapers"))

from calculators.rebar_calculator import RebarShippingCalculator
from calculators.rebar_cli import main as rebar_cli_main

def main():
    """Main application entry point"""
    print("🏗️ REBAR SHIPPING CALCULATOR FOR ISRAEL")
    print("=" * 50)
    print()
    print("Available options:")
    print("1. Calculate CIF prices for rebar shipments")
    print("2. Compare routes")
    print("3. Run data scrapers")
    print("4. Exit")
    print()
    
    while True:
        try:
            choice = input("Select option (1-4): ").strip()
            
            if choice == "1":
                run_calculator()
            elif choice == "2":
                run_comparison()
            elif choice == "3":
                run_scrapers()
            elif choice == "4":
                print("👋 Goodbye!")
                break
            else:
                print("❌ Invalid choice. Please select 1-4.")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

def run_calculator():
    """Run the rebar calculator"""
    print("\n🔩 REBAR CIF CALCULATOR")
    print("-" * 30)
    
    try:
        # Get user inputs
        port = input("Origin port (e.g., Mersin, Shanghai): ").strip()
        tonnage = int(input("Tonnage (tons): ").strip())
        grade = input("Steel grade (C-500/B-500) [C-500]: ").strip() or "C-500"
        
        # Calculate
        calculator = RebarShippingCalculator()
        result = calculator.calculate_rebar_cif_price(port, tonnage, grade)
        
        # Display results
        print(f"\n💰 CIF BREAKDOWN:")
        print(f"FOB Price:    ${result['cif_breakdown']['fob_price_per_ton']:.2f}/ton")
        print(f"Freight Cost: ${result['cif_breakdown']['freight_cost_per_ton']:.2f}/ton")
        print(f"Insurance:    ${result['cif_breakdown']['insurance_cost_per_ton']:.2f}/ton")
        print(f"CIF Total:    ${result['cif_breakdown']['cif_price_per_ton']:.2f}/ton")
        print(f"\nTotal Cost:   ${result['total_costs']['cif_total']:,.2f}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    
    input("\nPress Enter to continue...")

def run_comparison():
    """Run route comparison"""
    print("\n🏆 ROUTE COMPARISON")
    print("-" * 30)
    
    try:
        tonnage = int(input("Tonnage (tons): ").strip())
        grade = input("Steel grade (C-500/B-500) [C-500]: ").strip() or "C-500"
        
        calculator = RebarShippingCalculator()
        comparison = calculator.compare_rebar_routes(tonnage, grade)
        
        if not comparison.empty:
            # Add freight percentage
            comparison['Freight_Pct'] = (comparison['Freight_USD_per_ton'] / comparison['FOB_USD_per_ton'] * 100).round(1)
            
            print(f"\n📊 COMPARISON RESULTS:")
            print(f"{'Port':<12} {'Country':<8} {'FOB':<8} {'Freight':<8} {'Frt%':<6} {'CIF':<8}")
            print("-" * 60)
            
            for _, row in comparison.head(7).iterrows():
                print(f"{row['Origin_Port']:<12} {row['Country']:<8} "
                      f"${row['FOB_USD_per_ton']:<7.0f} ${row['Freight_USD_per_ton']:<7.0f} "
                      f"{row['Freight_Pct']:<5.1f}% ${row['CIF_USD_per_ton']:<7.0f}")
            
            best = comparison.iloc[0]
            print(f"\n🥇 BEST: {best['Origin_Port']} at ${best['CIF_USD_per_ton']:.2f}/ton")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    
    input("\nPress Enter to continue...")

def run_scrapers():
    """Run data scrapers"""
    print("\n🔍 DATA SCRAPERS")
    print("-" * 30)
    print("1. LME Steel Rebar Prices")
    print("2. Drewry Breakbulk Indices") 
    print("3. Kline Shipping Data")
    print("4. Back to main menu")
    
    choice = input("Select scraper (1-4): ").strip()
    
    if choice == "1":
        run_lme_scraper()
    elif choice == "2":
        run_drewry_scraper()
    elif choice == "3":
        run_kline_scraper()
    elif choice == "4":
        return
    else:
        print("❌ Invalid choice")

def run_lme_scraper():
    """Run LME scraper"""
    print("\n📡 Running LME Steel Rebar Scraper...")
    try:
        from scrapers.lme_api_extractor import LMEAPIExtractor
        extractor = LMEAPIExtractor()
        extractor.extract_api_data()
        print("✅ LME scraper completed!")
    except Exception as e:
        print(f"❌ LME scraper failed: {e}")

def run_drewry_scraper():
    """Run Drewry scraper"""
    print("\n📡 Running Drewry Breakbulk Scraper...")
    try:
        from scrapers.drewry_breakbulk_data_extractor import DrewryBreakbulkExtractor
        extractor = DrewryBreakbulkExtractor()
        extractor.extract_all_data()
        print("✅ Drewry scraper completed!")
    except Exception as e:
        print(f"❌ Drewry scraper failed: {e}")

def run_kline_scraper():
    """Run Kline scraper"""
    print("\n📡 Running Kline Shipping Scraper...")
    try:
        from scrapers.kline_complete_data_extractor import KlineDataExtractor
        extractor = KlineDataExtractor()
        extractor.extract_all_data()
        print("✅ Kline scraper completed!")
    except Exception as e:
        print(f"❌ Kline scraper failed: {e}")

if __name__ == "__main__":
    main()
