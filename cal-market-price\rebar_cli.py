#!/usr/bin/env python3
"""
Rebar Shipping CLI - Specialized for steel rods (rebar) CIF pricing to Israel
Calculates: CIF = FOB (LME) + Freight + Insurance
"""

import argparse
import sys
from pathlib import Path
from datetime import datetime

# Add current directory to path for imports
sys.path.append(str(Path(__file__).parent))

from rebar_calculator import RebarShippingCalculator

def main():
    parser = argparse.ArgumentParser(
        description='Calculate rebar (steel rods) CIF prices to Israel',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
🏗️  REBAR SHIPPING EXAMPLES:

  # Get CIF quote for 5,000 tons C-500 rebar from Shanghai
  python rebar_cli.py quote --port Shanghai --tonnage 5000 --grade C-500

  # Compare all ports for 3,000 tons
  python rebar_cli.py compare --tonnage 3000

  # Show available rebar ports
  python rebar_cli.py ports

  # Get current LME FOB price
  python rebar_cli.py fob-price

  # Export CIF comparison to Excel
  python rebar_cli.py export --tonnage 8000 --format excel

  # Update market data from all sources
  python rebar_cli.py update --source all

  # Update only LME data
  python rebar_cli.py update --source lme

  # Update data in background (non-blocking)
  python rebar_cli.py update --source all --async

📊 CIF BREAKDOWN: FOB (LME) + Freight (Baltic) + Insurance
🚢 VESSEL TYPES: Handysize/Supramax breakbulk (BSI/BHSI indices)
🔩 STEEL GRADES: C-500, B-500
🔄 DATA SOURCES: LME, Drewry, Kline (updateable via CLI)
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Quote command - detailed CIF breakdown
    quote_parser = subparsers.add_parser('quote', help='Get detailed CIF quote for rebar shipment')
    quote_parser.add_argument('--port', required=True, help='Origin port name')
    quote_parser.add_argument('--tonnage', type=int, required=True, help='Rebar tonnage (tons)')
    quote_parser.add_argument('--grade', choices=['C-500', 'B-500'], default='C-500', help='Steel grade')
    
    # Compare command - compare all ports
    compare_parser = subparsers.add_parser('compare', help='Compare CIF prices across all rebar ports')
    compare_parser.add_argument('--tonnage', type=int, required=True, help='Rebar tonnage (tons)')
    compare_parser.add_argument('--grade', choices=['C-500', 'B-500'], default='C-500', help='Steel grade')
    compare_parser.add_argument('--top', type=int, default=10, help='Show top N results')
    
    # Ports command - show available rebar ports
    ports_parser = subparsers.add_parser('ports', help='Show available rebar shipping ports')
    ports_parser.add_argument('--country', help='Filter by country')
    ports_parser.add_argument('--grade', help='Filter by available steel grade')
    
    # FOB price command
    fob_parser = subparsers.add_parser('fob-price', help='Get current LME FOB price')
    fob_parser.add_argument('--days', type=int, default=30, help='Days of price history')
    
    # Export command
    export_parser = subparsers.add_parser('export', help='Export CIF comparison to file')
    export_parser.add_argument('--tonnage', type=int, required=True, help='Rebar tonnage (tons)')
    export_parser.add_argument('--grade', choices=['C-500', 'B-500'], default='C-500', help='Steel grade')
    export_parser.add_argument('--format', choices=['csv', 'excel'], default='csv', help='Export format')
    export_parser.add_argument('--output', help='Output filename (auto-generated if not specified)')
    
    # Market command - show Baltic indices
    market_parser = subparsers.add_parser('market', help='Show current Baltic indices for rebar shipping')

    # Update command - update market data
    update_parser = subparsers.add_parser('update', help='Update market data from external sources')
    update_parser.add_argument('--source', choices=['lme', 'drewry', 'kline', 'all'],
                              help='Data source to update (default: all)', default='all')
    update_parser.add_argument('--async', action='store_true',
                              help='Run scrapers in background (non-blocking)')

    # Status command - show data status
    status_parser = subparsers.add_parser('status', help='Show current data status and last update times')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # Initialize calculator
    try:
        print("🏗️  Initializing Rebar Shipping Calculator...")
        calculator = RebarShippingCalculator()
        print("✓ Ready")
    except Exception as e:
        print(f"❌ Error initializing calculator: {e}")
        return
    
    # Execute commands
    try:
        if args.command == 'quote':
            handle_quote_command(calculator, args)
        elif args.command == 'compare':
            handle_compare_command(calculator, args)
        elif args.command == 'ports':
            handle_ports_command(calculator, args)
        elif args.command == 'fob-price':
            handle_fob_command(calculator, args)
        elif args.command == 'export':
            handle_export_command(calculator, args)
        elif args.command == 'market':
            handle_market_command(calculator, args)
        elif args.command == 'update':
            handle_update_command(args)
        elif args.command == 'status':
            handle_status_command(calculator, args)
    except Exception as e:
        print(f"❌ Error executing command: {e}")

def handle_quote_command(calculator, args):
    """Handle detailed CIF quote command"""
    print(f"\n🔩 Getting CIF quote for {args.tonnage:,} tons of {args.grade} rebar from {args.port}...")
    
    try:
        calculator.get_detailed_quote(args.port, args.tonnage, args.grade)
    except ValueError as e:
        print(f"❌ Error: {e}")
        print(f"\nAvailable ports: {', '.join(calculator.rebar_ports.keys())}")
    except Exception as e:
        print(f"❌ Calculation error: {e}")

def handle_compare_command(calculator, args):
    """Handle CIF comparison command"""
    print(f"\n🏆 Comparing CIF prices for {args.tonnage:,} tons of {args.grade} rebar...")
    
    try:
        comparison = calculator.compare_rebar_routes(args.tonnage, args.grade)
        
        if comparison.empty:
            print("❌ No results available")
            return
        
        # Add freight percentage column
        comparison['Freight_Pct_of_FOB'] = (comparison['Freight_USD_per_ton'] / comparison['FOB_USD_per_ton'] * 100).round(1)

        print(f"\n📊 REBAR CIF PRICE COMPARISON ({args.grade}):")
        print("-" * 100)
        print(f"{'Rank':<4} {'Port':<12} {'Country':<8} {'FOB':<8} {'Freight':<8} {'Frt%':<6} {'Insurance':<9} {'CIF':<8} {'Total':<12}")
        print("-" * 100)

        for i, (_, row) in enumerate(comparison.head(args.top).iterrows(), 1):
            print(f"{i:<4} {row['Origin_Port']:<12} {row['Country']:<8} "
                  f"${row['FOB_USD_per_ton']:<7.0f} ${row['Freight_USD_per_ton']:<7.0f} "
                  f"{row['Freight_Pct_of_FOB']:<5.1f}% "
                  f"${row['Insurance_USD_per_ton']:<8.2f} ${row['CIF_USD_per_ton']:<7.0f} "
                  f"${row['Total_CIF_USD']:<11,.0f}")
        
        # Highlight best option
        best = comparison.iloc[0]
        print(f"\n🥇 BEST OPTION: {best['Origin_Port']} ({best['Country']})")
        print(f"   FOB: ${best['FOB_USD_per_ton']:.2f}/ton | Freight: ${best['Freight_USD_per_ton']:.2f}/ton | Insurance: ${best['Insurance_USD_per_ton']:.2f}/ton")
        print(f"   CIF: ${best['CIF_USD_per_ton']:.2f}/ton | Total: ${best['Total_CIF_USD']:,.2f}")
        
    except Exception as e:
        print(f"❌ Comparison error: {e}")

def handle_ports_command(calculator, args):
    """Handle ports listing command"""
    ports = calculator.rebar_ports
    
    # Apply filters
    if args.country:
        ports = {k: v for k, v in ports.items() if v['country'].lower() == args.country.lower()}
    
    if args.grade:
        ports = {k: v for k, v in ports.items() if args.grade in v['steel_grade_available']}
    
    if not ports:
        print("❌ No ports found matching criteria")
        return
    
    print(f"\n🚢 AVAILABLE REBAR SHIPPING PORTS ({len(ports)} total):")
    print("-" * 90)
    print(f"{'Port':<12} {'Country':<8} {'Days':<6} {'Steel Grades':<15} {'Typical Tonnage':<15}")
    print("-" * 90)
    
    for port_name, port_info in sorted(ports.items()):
        grades = ', '.join(port_info['steel_grade_available'])
        print(f"{port_name:<12} {port_info['country']:<8} {port_info['voyage_days']:<6.1f} "
              f"{grades:<15} {port_info['typical_rebar_tonnage']:<15}")

def handle_fob_command(calculator, args):
    """Handle FOB price command"""
    print(f"\n💰 Fetching current LME FOB price...")
    
    try:
        fob_data = calculator.get_lme_fob_price()
        
        print(f"\n📈 CURRENT LME FOB PRICE:")
        print("-" * 30)
        print(f"Price: ${fob_data['fob_price_usd_ton']:.2f}/ton")
        print(f"Date: {fob_data['price_date'][:10]}")
        print(f"Grade: {fob_data['grade']}")
        print(f"Source: {fob_data['source']}")
        
    except Exception as e:
        print(f"❌ Error fetching FOB price: {e}")

def handle_export_command(calculator, args):
    """Handle export command"""
    print(f"\n📊 Generating CIF comparison for export...")
    
    try:
        comparison = calculator.compare_rebar_routes(args.tonnage, args.grade)
        
        if comparison.empty:
            print("❌ No data to export")
            return
        
        # Generate filename if not provided
        if not args.output:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            extension = 'xlsx' if args.format == 'excel' else 'csv'
            args.output = f"rebar_cif_{args.tonnage}t_{args.grade}_{timestamp}.{extension}"
        
        output_path = Path("reports") / args.output
        output_path.parent.mkdir(exist_ok=True)
        
        # Export based on format
        if args.format == 'excel':
            comparison.to_excel(output_path, index=False)
        else:
            comparison.to_csv(output_path, index=False)
        
        print(f"✓ Exported {len(comparison)} routes to: {output_path}")
        
        # Show summary
        best = comparison.iloc[0]
        print(f"\nBest option: {best['Origin_Port']} at ${best['CIF_USD_per_ton']:.2f}/ton CIF")
        
    except Exception as e:
        print(f"❌ Export error: {e}")

def handle_market_command(calculator, args):
    """Handle market data command"""
    print(f"\n📈 CURRENT MARKET DATA FOR REBAR SHIPPING:")
    print("-" * 50)
    
    try:
        # Show Baltic indices
        baltic_rates = calculator._get_current_baltic_rates()
        if baltic_rates:
            print("\n🌊 Baltic Indices (Rebar-Relevant):")
            for index, data in baltic_rates.items():
                relevance = "HIGH" if index in ['BSI', 'BHSI'] else "MEDIUM"
                print(f"  {index}: {data['value']:.0f} USD/day ({relevance} relevance) - {data['date']}")
        
        # Show FOB price
        fob_data = calculator.get_lme_fob_price()
        print(f"\n💰 LME FOB Price:")
        print(f"  Steel/Rebar: ${fob_data['fob_price_usd_ton']:.2f}/ton - {fob_data['price_date'][:10]}")
        
        # Show market conditions
        print(f"\n⚙️ Market Conditions:")
        conditions = calculator.market_conditions
        print(f"  Bunker Price: ${conditions['bunker_price_usd_mt']}/MT")
        print(f"  Marine Insurance: {conditions['marine_insurance_rate']*100:.2f}% of cargo value")
        print(f"  Suez Canal Fee: ${conditions['suez_canal_fee']:,}")
        print(f"  Red Sea Surcharge: {conditions['red_sea_surcharge_rate']*100:.0f}% (China routes)")

    except Exception as e:
        print(f"❌ Error fetching market data: {e}")

def handle_update_command(args):
    """Handle data update command"""
    import threading
    import sys
    from pathlib import Path

    # Add scrapers directory to path
    scrapers_path = Path(__file__).parent / "scrapers"
    sys.path.append(str(scrapers_path))

    print(f"\n🔄 UPDATING MARKET DATA")
    print("-" * 40)

    def run_lme_scraper():
        """Run LME scraper"""
        try:
            print("📡 Starting LME Steel Rebar scraper...")
            from lme_api_extractor import LMEAPIExtractor
            extractor = LMEAPIExtractor()
            extractor.extract_api_data()
            print("✅ LME scraper completed!")
        except Exception as e:
            print(f"❌ LME scraper failed: {e}")

    def run_drewry_scraper():
        """Run Drewry scraper"""
        try:
            print("📡 Starting Drewry Breakbulk scraper...")
            from drewry_breakbulk_data_extractor import DrewryBreakbulkExtractor
            extractor = DrewryBreakbulkExtractor()
            extractor.extract_all_data()
            print("✅ Drewry scraper completed!")
        except Exception as e:
            print(f"❌ Drewry scraper failed: {e}")

    def run_kline_scraper():
        """Run Kline scraper"""
        try:
            print("📡 Starting Kline shipping scraper...")
            from kline_complete_data_extractor import KlineDataExtractor
            extractor = KlineDataExtractor()
            extractor.extract_all_data()
            print("✅ Kline scraper completed!")
        except Exception as e:
            print(f"❌ Kline scraper failed: {e}")

    # Determine which scrapers to run
    scrapers_to_run = []

    if args.source in ['lme', 'all']:
        scrapers_to_run.append(('LME Steel Prices', run_lme_scraper))

    if args.source in ['drewry', 'all']:
        scrapers_to_run.append(('Drewry Breakbulk Indices', run_drewry_scraper))

    if args.source in ['kline', 'all']:
        scrapers_to_run.append(('Kline Shipping Data', run_kline_scraper))

    if not scrapers_to_run:
        print("❌ No valid data source specified")
        return

    print(f"📊 Updating {len(scrapers_to_run)} data source(s): {args.source}")

    if getattr(args, 'async', False):
        # Run scrapers in background threads
        print("🔄 Running scrapers in background...")
        threads = []
        for name, scraper_func in scrapers_to_run:
            thread = threading.Thread(target=scraper_func, name=name)
            thread.start()
            threads.append(thread)
            print(f"🚀 Started {name} scraper in background")

        print(f"\n💡 {len(threads)} scraper(s) running in background")
        print("   Check the browser windows for progress")
        print("   Data will be updated automatically when complete")

    else:
        # Run scrapers sequentially
        print("🔄 Running scrapers sequentially...")
        for i, (name, scraper_func) in enumerate(scrapers_to_run, 1):
            print(f"\n[{i}/{len(scrapers_to_run)}] {name}")
            print("-" * 30)
            scraper_func()

        print(f"\n🎉 All {len(scrapers_to_run)} scraper(s) completed!")
        print("💡 Updated data is now available for calculations")

def handle_status_command(calculator, args):
    """Handle status command - show data status"""
    import os
    from datetime import datetime
    from pathlib import Path

    print(f"\n📊 DATA STATUS REPORT")
    print("-" * 50)

    data_path = Path(__file__).parent / "data"

    # Check LME data
    print(f"\n🏗️ LME Steel Rebar Data:")
    lme_files = [
        ("Latest Price", data_path / "lme_data" / "latest_steel_price.csv"),
        ("Historical Data", data_path / "lme_data" / "LME_Steel_Rebar_data.csv"),
    ]

    for name, file_path in lme_files:
        if file_path.exists():
            mod_time = datetime.fromtimestamp(file_path.stat().st_mtime)
            print(f"  ✅ {name}: {mod_time.strftime('%Y-%m-%d %H:%M')}")

            # Show current price if it's the latest price file
            if "latest" in name.lower():
                try:
                    import pandas as pd
                    df = pd.read_csv(file_path)
                    if not df.empty:
                        price = df.iloc[-1]['price_usd_ton']
                        date = df.iloc[-1]['date']
                        print(f"      Current FOB: ${price}/ton ({date})")
                except:
                    pass
        else:
            print(f"  ❌ {name}: Not found")

    # Check Drewry data
    print(f"\n🚢 Drewry Breakbulk Data:")
    drewry_path = data_path / "breakbulk_indices"
    if drewry_path.exists():
        drewry_files = list(drewry_path.glob("*_Index_data.csv"))
        if drewry_files:
            for file_path in sorted(drewry_files):
                mod_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                index_name = file_path.stem.replace('_data', '')
                print(f"  ✅ {index_name}: {mod_time.strftime('%Y-%m-%d %H:%M')}")
        else:
            print(f"  ❌ No Drewry index files found")
    else:
        print(f"  ❌ Drewry data directory not found")

    # Check Baltic/Kline data
    print(f"\n🌊 Baltic/Kline Shipping Data:")
    shipping_path = data_path / "shipping_indices"
    if shipping_path.exists():
        baltic_files = list(shipping_path.glob("*_data.csv"))
        if baltic_files:
            for file_path in sorted(baltic_files):
                mod_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                index_name = file_path.stem.replace('_data', '')
                print(f"  ✅ {index_name}: {mod_time.strftime('%Y-%m-%d %H:%M')}")
        else:
            print(f"  ❌ No Baltic/Kline index files found")
    else:
        print(f"  ❌ Shipping indices directory not found")

    # Show current market rates
    try:
        fob_data = calculator.get_lme_fob_price()
        baltic_rates = calculator._get_current_baltic_rates()

        print(f"\n💰 Current Market Rates:")
        print(f"  FOB (LME): ${fob_data['fob_price_usd_ton']:.2f}/ton")
        print(f"  Source: {fob_data['source']}")

        if baltic_rates:
            print(f"  Baltic Indices:")
            for index, data in baltic_rates.items():
                print(f"    {index}: {data['value']:.0f} USD/day")
    except Exception as e:
        print(f"  ❌ Error loading current rates: {e}")

    print(f"\n🔄 Update Commands:")
    print(f"  python rebar_cli.py update --source lme     # Update LME data")
    print(f"  python rebar_cli.py update --source drewry  # Update Drewry data")
    print(f"  python rebar_cli.py update --source kline   # Update Kline data")
    print(f"  python rebar_cli.py update --source all     # Update all data")

if __name__ == '__main__':
    main()
