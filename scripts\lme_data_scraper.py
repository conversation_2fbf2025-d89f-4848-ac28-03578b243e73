from playwright.sync_api import sync_playwright
import json
import csv
import os
from datetime import datetime, timedelta
from pathlib import Path
import re
import time

class LMEDataScraper:
    def __init__(self):
        self.base_url = 'https://www.lme.com'
        self.output_dir = Path('data')
        self.lme_data_dir = self.output_dir / 'lme_data'
        self.raw_dir = self.output_dir / 'lme_raw_extracts'
        
        # Create directories
        for dir_path in [self.output_dir, self.lme_data_dir, self.raw_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
    
    def scrape_steel_prices(self):
        """
        Scrape LME steel/rebar related prices using Playwright
        """
        print("🏗️ LME Steel Price Scraper")
        print("=" * 50)
        
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=False)
            context = browser.new_context()
            page = context.new_page()
            
            try:
                # Navigate to LME main page
                print(f"📡 Loading LME main page...")
                page.goto(self.base_url, wait_until='networkidle')
                page.wait_for_timeout(3000)
                
                # Look for steel/rebar related data
                steel_data = self._extract_steel_data(page)
                
                # Try to navigate to specific steel sections
                steel_sections = [
                    '/metals/non-ferrous/steel-billet',
                    '/metals/ferrous',
                    '/trading-data',
                    '/prices-and-data'
                ]
                
                all_data = []
                
                for section in steel_sections:
                    try:
                        print(f"📊 Checking section: {section}")
                        page.goto(f"{self.base_url}{section}", wait_until='networkidle')
                        page.wait_for_timeout(2000)
                        
                        # Extract any price data from this section
                        section_data = self._extract_price_data(page, section)
                        if section_data:
                            all_data.extend(section_data)
                            
                    except Exception as e:
                        print(f"⚠️ Could not access {section}: {e}")
                        continue
                
                # Try to find the specific API endpoint data
                api_data = self._try_api_extraction(page)
                if api_data:
                    all_data.extend(api_data)
                
                # Save all collected data
                self._save_steel_data(all_data)
                
                browser.close()
                
            except Exception as e:
                print(f"❌ Error during scraping: {e}")
                browser.close()
                raise
    
    def _extract_steel_data(self, page):
        """Extract steel-related price data from current page"""
        steel_data = []
        
        try:
            # Look for price tables
            price_tables = page.query_selector_all('table')
            for table in price_tables:
                table_text = table.inner_text().lower()
                if any(keyword in table_text for keyword in ['steel', 'rebar', 'billet', 'iron']):
                    rows = table.query_selector_all('tr')
                    for row in rows:
                        cells = row.query_selector_all('td, th')
                        if len(cells) >= 2:
                            row_data = [cell.inner_text().strip() for cell in cells]
                            steel_data.append({
                                'type': 'table_data',
                                'data': row_data,
                                'timestamp': datetime.now().isoformat()
                            })
            
            # Look for price displays
            price_elements = page.query_selector_all('[class*="price"], [class*="value"], [data-price]')
            for element in price_elements:
                text = element.inner_text().strip()
                if text and ('$' in text or '£' in text or '€' in text):
                    steel_data.append({
                        'type': 'price_element',
                        'value': text,
                        'timestamp': datetime.now().isoformat()
                    })
            
        except Exception as e:
            print(f"⚠️ Error extracting steel data: {e}")
        
        return steel_data
    
    def _extract_price_data(self, page, section):
        """Extract price data from specific section"""
        price_data = []
        
        try:
            # Wait for any dynamic content to load
            page.wait_for_timeout(2000)
            
            # Look for charts or data containers
            chart_containers = page.query_selector_all('[class*="chart"], [class*="graph"], [class*="data"]')
            
            for container in chart_containers:
                # Try to extract data from chart containers
                container_text = container.inner_text()
                if container_text and len(container_text) > 10:
                    price_data.append({
                        'section': section,
                        'type': 'chart_container',
                        'content': container_text[:500],  # Limit content length
                        'timestamp': datetime.now().isoformat()
                    })
            
            # Look for specific price patterns
            page_content = page.content()
            price_patterns = [
                r'\$\s*\d+\.?\d*',  # Dollar prices
                r'£\s*\d+\.?\d*',   # Pound prices
                r'€\s*\d+\.?\d*',   # Euro prices
                r'\d+\.?\d*\s*USD', # USD prices
            ]
            
            for pattern in price_patterns:
                matches = re.findall(pattern, page_content)
                for match in matches[:10]:  # Limit to first 10 matches
                    price_data.append({
                        'section': section,
                        'type': 'price_pattern',
                        'value': match,
                        'timestamp': datetime.now().isoformat()
                    })
            
        except Exception as e:
            print(f"⚠️ Error extracting from {section}: {e}")
        
        return price_data
    
    def _try_api_extraction(self, page):
        """Try to extract data from API calls or JavaScript variables"""
        api_data = []
        
        try:
            # Execute JavaScript to look for price data in window variables
            js_data = page.evaluate("""
                () => {
                    const data = [];
                    
                    // Look for common price data variables
                    const possibleVars = ['priceData', 'chartData', 'lmeData', 'prices'];
                    
                    for (const varName of possibleVars) {
                        if (window[varName]) {
                            data.push({
                                variable: varName,
                                data: window[varName]
                            });
                        }
                    }
                    
                    // Look for Highcharts data
                    if (window.Highcharts && window.Highcharts.charts) {
                        window.Highcharts.charts.forEach((chart, index) => {
                            if (chart && chart.series) {
                                chart.series.forEach((series, seriesIndex) => {
                                    if (series.data && series.data.length > 0) {
                                        data.push({
                                            type: 'highcharts',
                                            chartIndex: index,
                                            seriesIndex: seriesIndex,
                                            seriesName: series.name,
                                            dataPoints: series.data.slice(0, 10).map(point => ({
                                                x: point.x,
                                                y: point.y,
                                                category: point.category
                                            }))
                                        });
                                    }
                                });
                            }
                        });
                    }
                    
                    return data;
                }
            """)
            
            if js_data:
                api_data.extend(js_data)
                print(f"✓ Extracted {len(js_data)} JavaScript data objects")
            
        except Exception as e:
            print(f"⚠️ Error extracting API data: {e}")
        
        return api_data
    
    def _save_steel_data(self, all_data):
        """Save extracted steel data to files"""
        if not all_data:
            print("❌ No data extracted")
            return
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save raw data as JSON
        raw_file = self.raw_dir / f'lme_steel_data_{timestamp}.json'
        with open(raw_file, 'w') as f:
            json.dump(all_data, f, indent=2, default=str)
        print(f"💾 Saved raw data: {raw_file}")
        
        # Process and save as CSV for price data
        price_data = []
        current_price = None
        
        for item in all_data:
            if item.get('type') == 'price_element' or item.get('type') == 'price_pattern':
                value_text = item.get('value', '')
                # Try to extract numeric price
                price_match = re.search(r'(\d+\.?\d*)', value_text.replace(',', ''))
                if price_match:
                    try:
                        price_value = float(price_match.group(1))
                        # Filter reasonable steel prices (typically $300-$800 per ton)
                        if 200 <= price_value <= 1000:
                            price_data.append({
                                'date': datetime.now().strftime('%Y-%m-%d'),
                                'price_usd_ton': price_value,
                                'source': 'LME_scraper',
                                'raw_value': value_text,
                                'extraction_type': item.get('type')
                            })
                            current_price = price_value
                    except ValueError:
                        continue
        
        # If we found price data, save it
        if price_data:
            csv_file = self.lme_data_dir / f'steel_prices_{timestamp}.csv'
            with open(csv_file, 'w', newline='') as f:
                writer = csv.DictWriter(f, fieldnames=['date', 'price_usd_ton', 'source', 'raw_value', 'extraction_type'])
                writer.writeheader()
                writer.writerows(price_data)
            print(f"💾 Saved price data: {csv_file}")
            
            # Save latest price for easy access
            latest_file = self.lme_data_dir / 'latest_steel_price.csv'
            with open(latest_file, 'w', newline='') as f:
                writer = csv.DictWriter(f, fieldnames=['date', 'price_usd_ton', 'source'])
                writer.writeheader()
                if current_price:
                    writer.writerow({
                        'date': datetime.now().strftime('%Y-%m-%d'),
                        'price_usd_ton': current_price,
                        'source': 'LME_scraper'
                    })
            print(f"💾 Saved latest price: {latest_file}")
        
        # Generate summary
        summary = {
            'extraction_date': datetime.now().isoformat(),
            'total_data_points': len(all_data),
            'price_data_points': len(price_data),
            'latest_price_usd_ton': current_price,
            'files_created': [
                str(raw_file.name),
                str(csv_file.name) if price_data else None,
                'latest_steel_price.csv' if current_price else None
            ]
        }
        
        summary_file = self.lme_data_dir / f'extraction_summary_{timestamp}.json'
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2, default=str)
        
        print(f"\n📊 EXTRACTION SUMMARY:")
        print(f"Total data points: {len(all_data)}")
        print(f"Price data points: {len(price_data)}")
        if current_price:
            print(f"Latest price found: ${current_price:.2f}/ton")
        print(f"Files saved in: {self.lme_data_dir}")

def main():
    scraper = LMEDataScraper()
    
    try:
        scraper.scrape_steel_prices()
        print("\n✅ LME scraping completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Scraping failed: {e}")
        print("💡 Try running again or check LME website accessibility")

if __name__ == "__main__":
    main()
