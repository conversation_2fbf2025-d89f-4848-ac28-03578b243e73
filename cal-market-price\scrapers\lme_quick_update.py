#!/usr/bin/env python3
"""
Quick LME Data Update - Process existing captured JSON data
This is faster than running the full Playwright scraper
"""

import json
import pandas as pd
from datetime import datetime
from pathlib import Path

class LMEQuickUpdate:
    """
    Quick processor for already captured LME JSON data
    """
    
    def __init__(self):
        self.data_path = Path(__file__).parent.parent / "data"
        self.lme_data_dir = self.data_path / "lme_data"
        
        # Create directories
        for dir_path in [self.data_path, self.lme_data_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
    
    def update_from_captured_data(self):
        """
        Update LME data from the most recent captured JSON
        """
        print("⚡ LME Quick Update - Processing Captured Data")
        print("=" * 50)
        
        # Find the most recent JSON response file in lme_data folder
        json_files = list(self.lme_data_dir.glob("lme_json_responses_*.json"))

        if not json_files:
            print("❌ No captured JSON files found")
            print(f"📁 Checked: {self.lme_data_dir}")
            print("💡 Run the full scraper first: python scrapers/lme_api_extractor.py")
            return False
        
        latest_file = max(json_files, key=lambda x: x.stat().st_mtime)
        print(f"📁 Processing: {latest_file.name}")
        
        try:
            with open(latest_file, 'r') as f:
                captured_data = json.load(f)
            
            # Process each JSON response
            price_data = []
            for response in captured_data:
                if 'chart-data' in response.get('url', ''):
                    prices = self._extract_price_data(response['data'])
                    if prices:
                        price_data.extend(prices)
            
            if price_data:
                self._save_processed_data(price_data)
                print(f"✅ Successfully updated LME data with {len(price_data)} price points")
                return True
            else:
                print("❌ No price data found in captured responses")
                return False
                
        except Exception as e:
            print(f"❌ Error processing captured data: {e}")
            return False
    
    def _extract_price_data(self, api_data):
        """Extract price data from LME API response"""
        price_data = []
        
        try:
            labels = api_data.get('Labels', [])
            datasets = api_data.get('Datasets', [])
            
            print(f"📊 Found {len(labels)} dates and {len(datasets)} datasets")
            
            # Process ALL contract types (let user choose later)
            contract_priority_map = {
                'Month 1': 1, 'Month 2': 2, 'Month 3': 3, 'Month 4': 4, 'Month 5': 5, 'Month 6': 6,
                'Month 7': 7, 'Month 8': 8, 'Month 9': 9, 'Month 10': 10, 'Month 11': 11, 'Month 12': 12,
                'Month 13': 13, 'Month 14': 14, 'Month 15': 15
            }

            for dataset in datasets:
                if dataset.get('Label') == 'Price':
                    prices = dataset.get('Data', [])
                    row_title = dataset.get('RowTitle', '')

                    # Process all contract types
                    if row_title in contract_priority_map:
                        priority = contract_priority_map[row_title]
                        print(f"💰 Processing: {row_title} (Priority: {priority})")

                        # Combine dates with prices
                        for date_str, price_str in zip(labels, prices):
                            try:
                                # Parse date (format: MM/DD/YYYY)
                                date_obj = datetime.strptime(date_str, '%m/%d/%Y')
                                formatted_date = date_obj.strftime('%Y-%m-%d')

                                # Parse price
                                price_value = float(price_str)

                                price_data.append({
                                    'date': formatted_date,
                                    'price_usd_ton': price_value,
                                    'source': 'LME_Steel_Rebar_FOB_Turkey',
                                    'contract': row_title,
                                    'contract_priority': priority,
                                    'extraction_timestamp': datetime.now().isoformat()
                                })

                            except (ValueError, TypeError) as e:
                                continue
                    else:
                        print(f"⏭️ Skipping: {row_title} (unknown contract type)")
            
            # Sort by date
            price_data.sort(key=lambda x: x['date'])

            # Filter to get current/recent prices (keep all contracts)
            from datetime import timedelta
            today = datetime.now()
            current_prices = [p for p in price_data
                            if datetime.strptime(p['date'], '%Y-%m-%d') <= today + timedelta(days=30)]

            if current_prices:
                # Find 1-month contract for default display
                month1_prices = [p for p in current_prices if p.get('contract') == 'Month 1']
                if month1_prices:
                    latest = month1_prices[-1]
                    print(f"🎯 Default Price (1-Month Contract): ${latest['price_usd_ton']:.2f}/ton ({latest['date']})")
                else:
                    latest = current_prices[-1]
                    print(f"🎯 Latest Price ({latest.get('contract', 'Unknown')}): ${latest['price_usd_ton']:.2f}/ton ({latest['date']})")

                # Keep all current contract data (don't filter to just 1-month)
                price_data = current_prices

                # Show available contracts
                available_contracts = sorted(set(p['contract'] for p in current_prices),
                                           key=lambda x: int(x.split()[1]) if 'Month' in x else 999)
                print(f"📋 Available Contracts: {', '.join(available_contracts)}")

            elif price_data:
                latest = price_data[-1]
                print(f"🎯 Latest Available ({latest.get('contract', 'Unknown')}): ${latest['price_usd_ton']:.2f}/ton ({latest['date']})")
            else:
                print("❌ No price data found")
            
        except Exception as e:
            print(f"❌ Error extracting price data: {e}")
        
        return price_data
    
    def _save_processed_data(self, price_data):
        """Save processed price data - update existing files instead of creating backups"""

        # Save/update detailed price data as single CSV file with all columns
        df = pd.DataFrame(price_data)

        # Ensure all important columns are present and ordered logically
        column_order = ['date', 'price_usd_ton', 'contract', 'contract_priority', 'source', 'extraction_timestamp']
        df = df.reindex(columns=column_order)

        csv_file = self.lme_data_dir / 'steel_rebar_prices.csv'
        df.to_csv(csv_file, index=False)
        print(f"💾 Updated steel rebar prices: {csv_file.name} (with contract details)")

        # Save latest price for easy access by rebar calculator
        latest_price = price_data[-1]
        latest_df = pd.DataFrame([{
            'date': latest_price['date'],
            'price_usd_ton': latest_price['price_usd_ton'],
            'source': 'LME_Steel_Rebar_FOB_Turkey'
        }])
        latest_file = self.lme_data_dir / 'latest_steel_price.csv'
        latest_df.to_csv(latest_file, index=False)
        print(f"💾 Updated latest price: {latest_file.name}")

        # Save in detailed format for integration (keep all important columns)
        history_df = df[['date', 'price_usd_ton', 'contract', 'contract_priority', 'source', 'extraction_timestamp']].copy()

        # Sort by date and contract priority (1-month first)
        history_df = history_df.sort_values(['date', 'contract_priority'])

        # Remove duplicates by keeping the highest priority contract per date
        history_df = history_df.drop_duplicates(subset=['date'], keep='first')

        # Rename columns for clarity
        history_df = history_df.rename(columns={
            'price_usd_ton': 'value',
            'contract': 'lme_contract',
            'contract_priority': 'priority',
            'extraction_timestamp': 'last_updated'
        })

        history_file = self.lme_data_dir / 'LME_Steel_Rebar_data.csv'
        history_df.to_csv(history_file, index=False)
        print(f"💾 Updated price history: {history_file.name} (with contract details)")

        print(f"\n🎯 UPDATED DATA SUMMARY:")
        print(f"Default FOB Price: ${latest_price['price_usd_ton']:.2f}/ton (1-Month Contract)")
        print(f"Date: {latest_price['date']}")
        print(f"Total Data Points: {len(price_data)} (all available contracts)")

        # Show contract breakdown
        contract_counts = {}
        for item in price_data:
            contract = item.get('contract', 'Unknown')
            contract_counts[contract] = contract_counts.get(contract, 0) + 1

        print(f"Contract Breakdown:")
        for contract in sorted(contract_counts.keys(), key=lambda x: int(x.split()[1]) if 'Month' in x else 999):
            count = contract_counts[contract]
            print(f"  {contract}: {count} data points")

def main():
    updater = LMEQuickUpdate()
    
    try:
        success = updater.update_from_captured_data()
        
        if success:
            print(f"\n✅ LME data updated successfully!")
            print(f"💡 Data is now available for calculations")
        else:
            print(f"\n❌ LME update failed")
            print(f"💡 Try running the full scraper: python scrapers/lme_api_extractor.py")
            
    except Exception as e:
        print(f"\n❌ Update failed: {e}")

if __name__ == "__main__":
    main()
