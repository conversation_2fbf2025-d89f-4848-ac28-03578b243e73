#!/usr/bin/env python3
"""
Quick LME Data Update - Process existing captured JSON data
This is faster than running the full Playwright scraper
"""

import json
import pandas as pd
from datetime import datetime
from pathlib import Path

class LMEQuickUpdate:
    """
    Quick processor for already captured LME JSON data
    """
    
    def __init__(self):
        self.data_path = Path(__file__).parent.parent / "data"
        self.lme_data_dir = self.data_path / "lme_data"
        
        # Create directories
        for dir_path in [self.data_path, self.lme_data_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
    
    def update_from_captured_data(self):
        """
        Update LME data from the most recent captured JSON
        """
        print("⚡ LME Quick Update - Processing Captured Data")
        print("=" * 50)
        
        # Find the most recent JSON response file in lme_data folder
        json_files = list(self.lme_data_dir.glob("lme_json_responses_*.json"))

        if not json_files:
            print("❌ No captured JSON files found")
            print(f"📁 Checked: {self.lme_data_dir}")
            print("💡 Run the full scraper first: python scrapers/lme_api_extractor.py")
            return False
        
        latest_file = max(json_files, key=lambda x: x.stat().st_mtime)
        print(f"📁 Processing: {latest_file.name}")
        
        try:
            with open(latest_file, 'r') as f:
                captured_data = json.load(f)
            
            # Process each JSON response
            price_data = []
            for response in captured_data:
                if 'chart-data' in response.get('url', ''):
                    prices = self._extract_price_data(response['data'])
                    if prices:
                        price_data.extend(prices)
            
            if price_data:
                self._save_processed_data(price_data)
                print(f"✅ Successfully updated LME data with {len(price_data)} price points")
                return True
            else:
                print("❌ No price data found in captured responses")
                return False
                
        except Exception as e:
            print(f"❌ Error processing captured data: {e}")
            return False
    
    def _extract_price_data(self, api_data):
        """Extract price data from LME API response"""
        price_data = []
        
        try:
            labels = api_data.get('Labels', [])
            datasets = api_data.get('Datasets', [])
            
            print(f"📊 Found {len(labels)} dates and {len(datasets)} datasets")
            
            # Priority order for contracts (1-month is most important)
            contract_priority = ['Month 1', 'Month 2', 'Month 3']

            for dataset in datasets:
                if dataset.get('Label') == 'Price':
                    prices = dataset.get('Data', [])
                    row_title = dataset.get('RowTitle', '')

                    # Only process priority contracts (focus on 1-month)
                    if row_title in contract_priority:
                        print(f"💰 Processing: {row_title} (Priority Contract)")

                        # Combine dates with prices
                        for date_str, price_str in zip(labels, prices):
                            try:
                                # Parse date (format: MM/DD/YYYY)
                                date_obj = datetime.strptime(date_str, '%m/%d/%Y')
                                formatted_date = date_obj.strftime('%Y-%m-%d')

                                # Parse price
                                price_value = float(price_str)

                                price_data.append({
                                    'date': formatted_date,
                                    'price_usd_ton': price_value,
                                    'source': 'LME_Steel_Rebar_FOB_Turkey',
                                    'contract': row_title,
                                    'contract_priority': contract_priority.index(row_title) + 1,
                                    'extraction_timestamp': datetime.now().isoformat()
                                })

                            except (ValueError, TypeError) as e:
                                continue
                    else:
                        print(f"⏭️ Skipping: {row_title} (not priority contract)")
            
            # Sort by date
            price_data.sort(key=lambda x: x['date'])

            # Filter to get current/recent prices and prioritize 1-month contract
            from datetime import timedelta
            today = datetime.now()
            current_prices = [p for p in price_data
                            if datetime.strptime(p['date'], '%Y-%m-%d') <= today + timedelta(days=30)]

            # Prioritize 1-month contract for latest price
            month1_prices = [p for p in current_prices if p.get('contract') == 'Month 1']

            if month1_prices:
                latest = month1_prices[-1]
                print(f"🎯 Current Price (1-Month Contract): ${latest['price_usd_ton']:.2f}/ton ({latest['date']})")
                # Use 1-month contract prices as primary data
                price_data = month1_prices
            elif current_prices:
                latest = current_prices[-1]
                print(f"🎯 Current Price ({latest.get('contract', 'Unknown')}): ${latest['price_usd_ton']:.2f}/ton ({latest['date']})")
                price_data = current_prices
            elif price_data:
                latest = price_data[-1]
                print(f"🎯 Latest Available ({latest.get('contract', 'Unknown')}): ${latest['price_usd_ton']:.2f}/ton ({latest['date']})")
            else:
                print("❌ No price data found")
            
        except Exception as e:
            print(f"❌ Error extracting price data: {e}")
        
        return price_data
    
    def _save_processed_data(self, price_data):
        """Save processed price data - update existing files instead of creating backups"""

        # Save/update detailed price data as single CSV file
        df = pd.DataFrame(price_data)
        csv_file = self.lme_data_dir / 'steel_rebar_prices.csv'
        df.to_csv(csv_file, index=False)
        print(f"💾 Updated steel rebar prices: {csv_file.name}")

        # Save latest price for easy access by rebar calculator
        latest_price = price_data[-1]
        latest_df = pd.DataFrame([{
            'date': latest_price['date'],
            'price_usd_ton': latest_price['price_usd_ton'],
            'source': 'LME_Steel_Rebar_FOB_Turkey'
        }])
        latest_file = self.lme_data_dir / 'latest_steel_price.csv'
        latest_df.to_csv(latest_file, index=False)
        print(f"💾 Updated latest price: {latest_file.name}")

        # Save in standard format for integration
        history_df = df[['date', 'price_usd_ton']].copy()
        history_df.columns = ['date', 'value']
        history_df['source'] = 'LME_Steel_Rebar_FOB_Turkey'

        # Remove duplicates by taking the average price per date
        history_df = history_df.groupby('date').agg({
            'value': 'mean',
            'source': 'first'
        }).reset_index()

        history_file = self.lme_data_dir / 'LME_Steel_Rebar_data.csv'
        history_df.to_csv(history_file, index=False)
        print(f"💾 Updated price history: {history_file.name}")

        print(f"\n🎯 UPDATED DATA SUMMARY:")
        print(f"Latest FOB Price: ${latest_price['price_usd_ton']:.2f}/ton")
        print(f"Date: {latest_price['date']}")
        print(f"Contract: {latest_price.get('contract', 'N/A')} (Priority: 1-Month)")
        print(f"Data Points: {len(price_data)} (filtered for current contracts)")

def main():
    updater = LMEQuickUpdate()
    
    try:
        success = updater.update_from_captured_data()
        
        if success:
            print(f"\n✅ LME data updated successfully!")
            print(f"💡 Data is now available for calculations")
        else:
            print(f"\n❌ LME update failed")
            print(f"💡 Try running the full scraper: python scrapers/lme_api_extractor.py")
            
    except Exception as e:
        print(f"\n❌ Update failed: {e}")

if __name__ == "__main__":
    main()
