from playwright.sync_api import sync_playwright
import json
import csv
import pandas as pd
from datetime import datetime, timedelta
from pathlib import Path
import re
import requests

class LMEAPIExtractor:
    """
    Extract LME steel rebar data by capturing API endpoints and processing JSON responses
    """
    
    def __init__(self):
        self.steel_rebar_url = 'https://www.lme.com/Metals/Ferrous/LME-Steel-Rebar-FOB-Turkey-Platts#Price+graph'
        # Use relative path from current file location
        self.output_dir = Path(__file__).parent.parent / 'data'
        self.lme_data_dir = self.output_dir / 'lme_data'
        self.raw_dir = self.output_dir / 'lme_raw_extracts'
        
        # Create directories
        for dir_path in [self.output_dir, self.lme_data_dir, self.raw_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
    
    def extract_api_data(self):
        """
        Capture API endpoints and extract JSON data from LME steel rebar page
        """
        print("🔍 LME API Endpoint Extractor")
        print("=" * 50)
        
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=False)
            context = browser.new_context()
            page = context.new_page()
            
            # Storage for captured data
            api_endpoints = []
            json_responses = []
            
            # Network request handler
            def handle_request(request):
                url = request.url
                # Look for API endpoints
                if any(keyword in url.lower() for keyword in ['api', 'data', 'chart', 'trading', 'price']):
                    api_info = {
                        'url': url,
                        'method': request.method,
                        'headers': dict(request.headers),
                        'timestamp': datetime.now().isoformat()
                    }
                    api_endpoints.append(api_info)
                    print(f"🎯 API Endpoint Found: {url}")
            
            # Network response handler
            def handle_response(response):
                url = response.request.url
                if any(keyword in url.lower() for keyword in ['api', 'data', 'chart', 'trading', 'price']):
                    try:
                        content_type = response.headers.get('content-type', '').lower()
                        if 'json' in content_type:
                            json_data = response.json()
                            response_info = {
                                'url': url,
                                'status': response.status,
                                'content_type': content_type,
                                'data': json_data,
                                'timestamp': datetime.now().isoformat()
                            }
                            json_responses.append(response_info)
                            print(f"✅ JSON Response Captured: {url} (Status: {response.status})")
                    except Exception as e:
                        print(f"⚠️ Could not parse JSON from {url}: {e}")
            
            # Set up network monitoring
            page.on('request', handle_request)
            page.on('response', handle_response)
            
            try:
                # Navigate to LME steel rebar page
                print(f"📡 Loading: {self.steel_rebar_url}")
                page.goto(self.steel_rebar_url, wait_until='networkidle')
                
                # Wait for initial load
                print("⏳ Waiting for page to load completely...")
                page.wait_for_timeout(5000)

                # Check if we already have the chart data we need
                chart_data_found = any('chart-data' in resp['url'] and resp.get('data')
                                     for resp in json_responses)

                if chart_data_found:
                    print("✅ Chart data already captured, processing...")
                else:
                    # Try to trigger chart data loading
                    print("🔄 Triggering chart data loads...")
                    self._trigger_chart_loads(page)

                    # Wait for additional API calls (shorter timeout)
                    page.wait_for_timeout(3000)
                
                # Process captured data
                print(f"\n📊 Processing captured data...")
                print(f"API Endpoints found: {len(api_endpoints)}")
                print(f"JSON Responses captured: {len(json_responses)}")

                # Save and process the data
                price_data = self._process_captured_data(api_endpoints, json_responses)

                # Check if we got good price data
                if price_data and len(price_data) > 0:
                    print(f"✅ Successfully extracted {len(price_data)} price points")
                else:
                    print("⚠️ No price data extracted from captured responses")
                
                browser.close()
                return price_data
                
            except Exception as e:
                print(f"❌ Error during extraction: {e}")
                browser.close()
                raise
    
    def _trigger_chart_loads(self, page):
        """Try to trigger chart data loading by interacting with page elements (optimized)"""
        try:
            # Quick scroll to trigger any lazy loading
            page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
            page.wait_for_timeout(1000)
            page.evaluate("window.scrollTo(0, 0)")
            page.wait_for_timeout(1000)

            # Look for chart containers (limit to first few)
            chart_selectors = [
                '[class*="chart"]',
                '.highcharts-container'
            ]

            for selector in chart_selectors:
                elements = page.query_selector_all(selector)
                for element in elements[:1]:  # Try only first element
                    try:
                        element.hover()
                        page.wait_for_timeout(500)
                        element.click()
                        page.wait_for_timeout(1000)
                        break  # Exit after first successful interaction
                    except:
                        pass

            # Look for common date range buttons (quick check)
            date_buttons = page.query_selector_all('button')
            for button in date_buttons[:5]:  # Check only first 5 buttons
                try:
                    text = button.inner_text().lower()
                    if any(period in text for period in ['1y', 'year']):
                        button.click()
                        page.wait_for_timeout(2000)
                        break
                except:
                    pass

        except Exception as e:
            print(f"⚠️ Error triggering chart loads: {e}")
    
    def _process_captured_data(self, api_endpoints, json_responses):
        """Process captured API data and extract price information"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save raw API data
        api_file = self.raw_dir / f'lme_api_endpoints_{timestamp}.json'
        with open(api_file, 'w') as f:
            json.dump(api_endpoints, f, indent=2, default=str)
        print(f"💾 Saved API endpoints: {api_file}")
        
        # Save raw JSON responses
        json_file = self.raw_dir / f'lme_json_responses_{timestamp}.json'
        with open(json_file, 'w') as f:
            json.dump(json_responses, f, indent=2, default=str)
        print(f"💾 Saved JSON responses: {json_file}")
        
        # Extract price data from JSON responses
        price_data = []
        latest_price = None
        
        for response in json_responses:
            try:
                data = response['data']
                prices = self._extract_prices_from_json(data)
                if prices:
                    for price_info in prices:
                        price_info['source_url'] = response['url']
                        price_info['extraction_timestamp'] = response['timestamp']
                        price_data.append(price_info)
                        
                        # Track latest price
                        if price_info.get('price') and (not latest_price or price_info.get('date', '') > latest_price.get('date', '')):
                            latest_price = price_info
                            
            except Exception as e:
                print(f"⚠️ Error processing JSON response: {e}")
        
        # Save processed price data
        if price_data:
            # Save detailed price data
            df = pd.DataFrame(price_data)
            csv_file = self.lme_data_dir / f'steel_rebar_prices_{timestamp}.csv'
            df.to_csv(csv_file, index=False)
            print(f"💾 Saved price data: {csv_file}")
            
            # Save latest price for easy access
            if latest_price:
                latest_df = pd.DataFrame([{
                    'date': latest_price.get('date', datetime.now().strftime('%Y-%m-%d')),
                    'price_usd_ton': latest_price.get('price'),
                    'source': 'LME_API_Extractor'
                }])
                latest_file = self.lme_data_dir / 'latest_steel_price.csv'
                latest_df.to_csv(latest_file, index=False)
                print(f"💾 Saved latest price: {latest_file}")
                print(f"🎯 Latest Price Found: ${latest_price.get('price'):.2f}/ton")
        
        # Generate summary
        summary = {
            'extraction_timestamp': datetime.now().isoformat(),
            'api_endpoints_found': len(api_endpoints),
            'json_responses_captured': len(json_responses),
            'price_data_points': len(price_data),
            'latest_price': latest_price,
            'api_urls': [ep['url'] for ep in api_endpoints]
        }
        
        summary_file = self.lme_data_dir / f'extraction_summary_{timestamp}.json'
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2, default=str)
        
        print(f"\n📈 EXTRACTION SUMMARY:")
        print(f"API endpoints found: {len(api_endpoints)}")
        print(f"JSON responses: {len(json_responses)}")
        print(f"Price data points: {len(price_data)}")
        if latest_price:
            print(f"Latest price: ${latest_price.get('price'):.2f}/ton")
        
        return price_data
    
    def _extract_prices_from_json(self, json_data):
        """Extract price information from JSON data"""
        prices = []
        
        try:
            # Handle different JSON structures
            if isinstance(json_data, dict):
                # Look for common price data structures
                for key in ['data', 'series', 'prices', 'values', 'chartData']:
                    if key in json_data:
                        prices.extend(self._parse_price_array(json_data[key]))
                
                # Look for direct price values
                for key, value in json_data.items():
                    if 'price' in key.lower() and isinstance(value, (int, float)):
                        if 200 <= value <= 1000:  # Reasonable steel price range
                            prices.append({
                                'price': value,
                                'date': datetime.now().strftime('%Y-%m-%d'),
                                'field': key
                            })
            
            elif isinstance(json_data, list):
                prices.extend(self._parse_price_array(json_data))
                
        except Exception as e:
            print(f"⚠️ Error extracting prices from JSON: {e}")
        
        return prices
    
    def _parse_price_array(self, data_array):
        """Parse price data from array structures"""
        prices = []
        
        try:
            if isinstance(data_array, list):
                for item in data_array:
                    if isinstance(item, dict):
                        # Look for price and date fields
                        price_val = None
                        date_val = None
                        
                        for key, value in item.items():
                            if isinstance(value, (int, float)) and 200 <= value <= 1000:
                                if 'price' in key.lower() or 'value' in key.lower() or 'y' == key.lower():
                                    price_val = value
                            elif 'date' in key.lower() or 'time' in key.lower() or 'x' == key.lower():
                                date_val = value
                        
                        if price_val:
                            prices.append({
                                'price': price_val,
                                'date': self._format_date(date_val) if date_val else datetime.now().strftime('%Y-%m-%d'),
                                'raw_item': item
                            })
                    
                    elif isinstance(item, (list, tuple)) and len(item) >= 2:
                        # Handle [timestamp, price] format
                        try:
                            if isinstance(item[1], (int, float)) and 200 <= item[1] <= 1000:
                                prices.append({
                                    'price': item[1],
                                    'date': self._format_date(item[0]),
                                    'raw_item': item
                                })
                        except:
                            pass
                            
        except Exception as e:
            print(f"⚠️ Error parsing price array: {e}")
        
        return prices
    
    def _format_date(self, date_val):
        """Format various date formats to YYYY-MM-DD"""
        try:
            if isinstance(date_val, (int, float)):
                # Assume timestamp (milliseconds or seconds)
                if date_val > 1e12:  # Milliseconds
                    date_val = date_val / 1000
                dt = datetime.fromtimestamp(date_val)
                return dt.strftime('%Y-%m-%d')
            elif isinstance(date_val, str):
                # Try to parse string date
                for fmt in ['%Y-%m-%d', '%Y/%m/%d', '%d/%m/%Y', '%m/%d/%Y']:
                    try:
                        dt = datetime.strptime(date_val, fmt)
                        return dt.strftime('%Y-%m-%d')
                    except:
                        continue
        except:
            pass
        
        return datetime.now().strftime('%Y-%m-%d')

def main():
    extractor = LMEAPIExtractor()
    
    try:
        price_data = extractor.extract_api_data()
        
        if price_data:
            print(f"\n✅ Successfully extracted {len(price_data)} price data points!")
        else:
            print(f"\n⚠️ No price data extracted. Check the captured API endpoints.")
            
    except Exception as e:
        print(f"\n❌ Extraction failed: {e}")

if __name__ == "__main__":
    main()
